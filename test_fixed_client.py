#!/usr/bin/env python3
"""
Test the fixed client implementation
"""

import asyncio
import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from tests.test_config import APIClient
import time

async def test_fixed_client():
    """Test the fixed client implementation"""
    
    print("=== Testing Fixed Client Implementation ===")
    
    async with APIClient() as client:
        # Setup test data
        collection_name = f"fixed_test_{int(time.time())}"
        
        print("1. Ingesting test data...")
        status, data = await client.ingest_text(
            collection_name,
            "The secret password is BETA-456. This is confidential information.",
            "fixed_test",
            "fixed_1"
        )
        print(f"   Ingestion status: {status}")
        
        await asyncio.sleep(5)
        
        print("2. Testing inference with fixed client...")
        status_code, tokens = await client.inference_request(
            collection_name,
            "What is the secret password?"
        )
        
        print(f"   Status: {status_code}")
        print(f"   Tokens received: {len(tokens)}")
        print(f"   Response: {''.join(tokens)}")
        
        if len(tokens) > 0:
            print("✅ Fixed client is working correctly!")
        else:
            print("❌ Fixed client still has issues")

if __name__ == "__main__":
    asyncio.run(test_fixed_client())
