#!/usr/bin/env python3
"""
Debug streaming issues in SecuraMind
"""

import asyncio
import aiohttp
import json
import time

async def debug_streaming():
    """Debug the streaming response issue"""
    
    print("=== Streaming Debug Test ===")
    
    base_url = "http://127.0.0.1:8000"
    
    async with aiohttp.ClientSession() as session:
        # Setup test data
        collection_name = f"stream_debug_{int(time.time())}"
        
        # Ingest test data
        print("1. Ingesting test data...")
        ingestion_payload = {
            "collection_name": collection_name,
            "text_content": "The secret code is ALPHA-123. This is important information.",
            "source_name": "debug_test",
            "source_identifier": "debug_1"
        }
        
        async with session.post(f"{base_url}/api/v1/ingest/text", json=ingestion_payload) as response:
            print(f"   Ingestion status: {response.status}")
        
        await asyncio.sleep(5)
        
        # Test inference with detailed debugging
        print("\n2. Testing inference with detailed debugging...")
        inference_payload = {
            "collection_name": collection_name,
            "query": "What is the secret code?",
            "language": "en"
        }
        
        try:
            async with session.post(f"{base_url}/api/v1/inference", json=inference_payload) as response:
                print(f"   Response status: {response.status}")
                print(f"   Response headers: {dict(response.headers)}")
                
                if response.status != 200:
                    error_text = await response.text()
                    print(f"   Error: {error_text}")
                    return
                
                print("   Raw streaming data:")
                line_count = 0
                token_count = 0
                
                async for chunk in response.content.iter_chunked(1024):
                    chunk_text = chunk.decode('utf-8', errors='ignore')
                    print(f"   Chunk {line_count}: {repr(chunk_text)}")
                    
                    # Process lines
                    for line in chunk_text.split('\n'):
                        line = line.strip()
                        if line.startswith('data: '):
                            try:
                                data_str = line[6:]
                                print(f"     Data string: {repr(data_str)}")
                                data = json.loads(data_str)
                                print(f"     Parsed data: {data}")
                                
                                if 'token' in data:
                                    token_count += 1
                                    print(f"     TOKEN {token_count}: '{data['token']}'")
                                elif 'event' in data:
                                    print(f"     EVENT: {data}")
                                    if data.get('event') == 'eos':
                                        print("     End of stream detected")
                                        return
                                        
                            except json.JSONDecodeError as e:
                                print(f"     JSON error: {e}")
                                print(f"     Raw data: {repr(data_str)}")
                        elif line:
                            print(f"     Non-data line: {repr(line)}")
                    
                    line_count += 1
                    if line_count > 50:  # Prevent infinite loop
                        print("   Breaking after 50 chunks")
                        break
                
                print(f"\n   Final token count: {token_count}")
                
        except Exception as e:
            print(f"   Exception: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_streaming())
