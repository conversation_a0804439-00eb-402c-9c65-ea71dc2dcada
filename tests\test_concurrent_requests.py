# tests/test_concurrent_requests.py
"""
Test concurrent request handling for SecuraMind API

Tests:
1. LLM routing (main -> backup when main busy)
2. Request queueing when both LLMs are busy
3. Proper request ordering and processing
"""

import asyncio
import time
import logging
from typing import List, Dict, Any
from .test_config import API<PERSON><PERSON>, TestResult, TestLogger, TEST_CONFIG, TEST_QUERIES

logger = logging.getLogger(__name__)

class ConcurrentRequestTests:
    """Test suite for concurrent request handling"""
    
    def __init__(self):
        self.test_logger = TestLogger("tests/concurrent_requests.log")
        self.results: List[TestResult] = []
        
    async def test_llm_routing_main_to_backup(self) -> TestResult:
        """Test that requests route from main LLM to backup when main is busy"""
        test_name = "LLM Routing: Main to Backup"
        self.test_logger.log_test_start(test_name)
        start_time = time.time()
        
        try:
            async with APIClient() as client:
                # First verify both LLMs are available
                health_ok, health_data = await client.health_check()
                if not health_ok:
                    raise Exception(f"Health check failed: {health_data}")
                
                if not health_data.get("main_llm_available") or not health_data.get("backup_llm_available"):
                    raise Exception("Both main and backup LLMs must be available for this test")
                
                # Setup test collection with data
                collection_name = f"test_routing_{int(time.time())}"
                status, data = await client.ingest_text(
                    collection_name, 
                    "Test data for LLM routing", 
                    "routing_test", 
                    "route_1"
                )
                
                if status != 200:
                    raise Exception(f"Failed to ingest test data: {data}")
                
                # Wait for ingestion to complete
                await asyncio.sleep(2)
                
                # Send two concurrent requests
                # The first should get main LLM, second should get backup
                tasks = []
                for i in range(2):
                    task = client.inference_request(collection_name, f"Test query {i}")
                    tasks.append(task)
                
                # Execute concurrently
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Verify both requests succeeded
                success_count = 0
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        logger.error(f"Request {i} failed: {result}")
                    else:
                        status_code, tokens = result
                        if status_code == 200 and len(tokens) > 0:
                            success_count += 1
                            logger.info(f"Request {i} succeeded with {len(tokens)} tokens")
                
                if success_count == 2:
                    result = TestResult(test_name, True, time.time() - start_time)
                else:
                    result = TestResult(
                        test_name, 
                        False, 
                        time.time() - start_time,
                        f"Only {success_count}/2 requests succeeded"
                    )
                    
        except Exception as e:
            result = TestResult(test_name, False, time.time() - start_time, str(e))
            
        self.test_logger.log_test_result(result)
        self.test_logger.log_test_end(test_name)
        return result
        
    async def test_request_queueing_when_llms_busy(self) -> TestResult:
        """Test that requests queue when both LLMs are busy"""
        test_name = "Request Queueing When LLMs Busy"
        self.test_logger.log_test_start(test_name)
        start_time = time.time()
        
        try:
            async with APIClient() as client:
                # Setup test collection
                collection_name = f"test_queue_{int(time.time())}"
                status, data = await client.ingest_text(
                    collection_name,
                    "Test data for queueing",
                    "queue_test",
                    "queue_1"
                )
                
                if status != 200:
                    raise Exception(f"Failed to ingest test data: {data}")
                
                await asyncio.sleep(2)
                
                # Send more requests than available LLMs (should be 2: main + backup)
                # This should test the queueing mechanism
                num_requests = 5
                tasks = []
                
                for i in range(num_requests):
                    task = client.inference_request(collection_name, f"Queue test query {i}")
                    tasks.append(task)
                
                # Execute all requests concurrently
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Verify all requests eventually succeeded (no timeouts/rejections)
                success_count = 0
                timeout_count = 0
                error_count = 0
                
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        error_count += 1
                        logger.error(f"Request {i} failed with exception: {result}")
                    else:
                        status_code, tokens = result
                        if status_code == 200 and len(tokens) > 0:
                            success_count += 1
                            logger.info(f"Request {i} succeeded with {len(tokens)} tokens")
                        elif status_code == 408:  # Timeout
                            timeout_count += 1
                            logger.warning(f"Request {i} timed out")
                        else:
                            error_count += 1
                            logger.error(f"Request {i} failed with status {status_code}")
                
                # All requests should succeed (queue and wait, don't reject)
                if success_count == num_requests:
                    result = TestResult(test_name, True, time.time() - start_time)
                else:
                    result = TestResult(
                        test_name,
                        False,
                        time.time() - start_time,
                        f"Only {success_count}/{num_requests} succeeded. Timeouts: {timeout_count}, Errors: {error_count}"
                    )
                    
        except Exception as e:
            result = TestResult(test_name, False, time.time() - start_time, str(e))
            
        self.test_logger.log_test_result(result)
        self.test_logger.log_test_end(test_name)
        return result
        
    async def test_request_ordering(self) -> TestResult:
        """Test that requests are processed in proper order"""
        test_name = "Request Ordering"
        self.test_logger.log_test_start(test_name)
        start_time = time.time()
        
        try:
            async with APIClient() as client:
                # Setup test collection
                collection_name = f"test_order_{int(time.time())}"
                status, data = await client.ingest_text(
                    collection_name,
                    "Test data for ordering",
                    "order_test", 
                    "order_1"
                )
                
                if status != 200:
                    raise Exception(f"Failed to ingest test data: {data}")
                
                await asyncio.sleep(2)
                
                # Send sequential requests with identifiable queries
                num_requests = 3
                request_times = []
                response_times = []
                
                for i in range(num_requests):
                    request_start = time.time()
                    request_times.append(request_start)
                    
                    status_code, tokens = await client.inference_request(
                        collection_name, 
                        f"Order test query number {i}"
                    )
                    
                    response_end = time.time()
                    response_times.append(response_end)
                    
                    if status_code != 200:
                        raise Exception(f"Request {i} failed with status {status_code}")
                    
                    logger.info(f"Request {i} completed in {response_end - request_start:.2f}s with {len(tokens)} tokens")
                
                # Verify requests completed in reasonable time
                total_time = response_times[-1] - request_times[0]
                if total_time > 180:  # 3 minutes max for 3 requests
                    raise Exception(f"Requests took too long: {total_time:.2f}s")
                
                result = TestResult(test_name, True, time.time() - start_time)
                
        except Exception as e:
            result = TestResult(test_name, False, time.time() - start_time, str(e))
            
        self.test_logger.log_test_result(result)
        self.test_logger.log_test_end(test_name)
        return result
        
    async def run_all_tests(self) -> List[TestResult]:
        """Run all concurrent request tests"""
        logger.info("Starting Concurrent Request Tests")
        
        tests = [
            self.test_llm_routing_main_to_backup,
            self.test_request_queueing_when_llms_busy,
            self.test_request_ordering
        ]
        
        for test_func in tests:
            result = await test_func()
            self.results.append(result)
            
            # Brief pause between tests
            await asyncio.sleep(1)
            
        return self.results

# Main execution for standalone testing
async def main():
    """Run concurrent request tests standalone"""
    test_suite = ConcurrentRequestTests()
    results = await test_suite.run_all_tests()
    
    # Summary
    passed = sum(1 for r in results if r.success)
    total = len(results)
    
    print(f"\n=== CONCURRENT REQUEST TESTS SUMMARY ===")
    print(f"Passed: {passed}/{total}")
    print(f"Failed: {total - passed}/{total}")
    
    for result in results:
        status = "PASS" if result.success else "FAIL"
        print(f"  {status}: {result.test_name} ({result.duration:.2f}s)")
        if not result.success:
            print(f"    Error: {result.error_message}")

if __name__ == "__main__":
    asyncio.run(main())
