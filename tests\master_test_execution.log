2025-06-22 17:31:34,267 - __main__ - INFO - Waiting for SecuraMind server to be ready...
2025-06-22 17:31:34,534 - __main__ - INFO - Server is ready. Starting comprehensive test suite...
2025-06-22 17:31:34,534 - __main__ - INFO - ================================================================================
2025-06-22 17:31:34,534 - __main__ - INFO - SECURAMIND API COMPREHENSIVE TEST SUITE
2025-06-22 17:31:34,535 - __main__ - INFO - ================================================================================
2025-06-22 17:31:34,535 - __main__ - INFO - Start time: 2025-06-22 17:31:34
2025-06-22 17:31:34,535 - __main__ - INFO - Test configuration: {'base_url': 'http://127.0.0.1:8000', 'timeout': 30, 'max_retries': 3, 'test_collection': 'test_collection', 'stress_test_requests': 1000, 'concurrent_test_requests': 100, 'request_timeout': 60, 'stream_timeout': 120, 'health_check_interval': 5, 'log_level': 'INFO'}
2025-06-22 17:31:34,535 - __main__ - INFO - Checking server health before starting tests...
2025-06-22 17:31:34,538 - __main__ - INFO - Server health check passed:
2025-06-22 17:31:34,538 - __main__ - INFO -   Main LLM: process_healthy (Available: True)
2025-06-22 17:31:34,538 - __main__ - INFO -   Backup LLM: process_healthy (Available: True)
2025-06-22 17:31:34,538 - __main__ - INFO -   Concurrent capacity: 2
2025-06-22 17:31:34,540 - __main__ - INFO - 
Checking server health before Concurrent Request Handling...
2025-06-22 17:31:34,541 - __main__ - INFO - Checking server health before starting tests...
2025-06-22 17:31:34,542 - __main__ - INFO - Server health check passed:
2025-06-22 17:31:34,543 - __main__ - INFO -   Main LLM: process_healthy (Available: True)
2025-06-22 17:31:34,543 - __main__ - INFO -   Backup LLM: process_healthy (Available: True)
2025-06-22 17:31:34,543 - __main__ - INFO -   Concurrent capacity: 2
2025-06-22 17:31:34,543 - __main__ - INFO - 
============================================================
2025-06-22 17:31:34,543 - __main__ - INFO - STARTING TEST SUITE: Concurrent Request Handling
2025-06-22 17:31:34,543 - __main__ - INFO - ============================================================
2025-06-22 17:31:34,543 - tests.test_concurrent_requests - INFO - Starting Concurrent Request Tests
2025-06-22 17:31:34,544 - root - INFO - === STARTING TEST: LLM Routing: Main to Backup ===
2025-06-22 17:31:49,120 - tests.test_concurrent_requests - INFO - Request 0 succeeded with 88 tokens
2025-06-22 17:31:49,120 - tests.test_concurrent_requests - INFO - Request 1 succeeded with 60 tokens
2025-06-22 17:31:49,121 - root - INFO - === TEST PASSED: LLM Routing: Main to Backup (Duration: 14.58s) ===
2025-06-22 17:31:49,121 - root - INFO - === COMPLETED TEST: LLM Routing: Main to Backup ===

2025-06-22 17:31:50,128 - root - INFO - === STARTING TEST: Request Queueing When LLMs Busy ===
2025-06-22 17:32:19,627 - tests.test_concurrent_requests - INFO - Request 0 succeeded with 52 tokens
2025-06-22 17:32:19,627 - tests.test_concurrent_requests - INFO - Request 1 succeeded with 191 tokens
2025-06-22 17:32:19,627 - tests.test_concurrent_requests - INFO - Request 2 succeeded with 56 tokens
2025-06-22 17:32:19,627 - tests.test_concurrent_requests - INFO - Request 3 succeeded with 62 tokens
2025-06-22 17:32:19,627 - tests.test_concurrent_requests - INFO - Request 4 succeeded with 58 tokens
2025-06-22 17:32:19,628 - root - INFO - === TEST PASSED: Request Queueing When LLMs Busy (Duration: 29.50s) ===
2025-06-22 17:32:19,628 - root - INFO - === COMPLETED TEST: Request Queueing When LLMs Busy ===

2025-06-22 17:32:20,639 - root - INFO - === STARTING TEST: Request Ordering ===
2025-06-22 17:32:28,163 - tests.test_concurrent_requests - INFO - Request 0 completed in 5.51s with 81 tokens
2025-06-22 17:32:38,215 - tests.test_concurrent_requests - INFO - Request 1 completed in 10.05s with 153 tokens
2025-06-22 17:32:44,473 - tests.test_concurrent_requests - INFO - Request 2 completed in 6.26s with 96 tokens
2025-06-22 17:32:44,473 - root - INFO - === TEST PASSED: Request Ordering (Duration: 23.83s) ===
2025-06-22 17:32:44,473 - root - INFO - === COMPLETED TEST: Request Ordering ===

2025-06-22 17:32:45,483 - __main__ - INFO - 
TEST SUITE COMPLETED: Concurrent Request Handling
2025-06-22 17:32:45,483 - __main__ - INFO -   Tests: 3/3 passed (100.0%)
2025-06-22 17:32:45,483 - __main__ - INFO -   Duration: 70.94s
2025-06-22 17:32:45,483 - __main__ - INFO - Pausing 10 seconds before next test suite...
2025-06-22 17:32:55,483 - __main__ - INFO - 
Checking server health before Concurrent Operations...
2025-06-22 17:32:55,483 - __main__ - INFO - Checking server health before starting tests...
2025-06-22 17:32:55,485 - __main__ - INFO - Server health check passed:
2025-06-22 17:32:55,486 - __main__ - INFO -   Main LLM: process_healthy (Available: True)
2025-06-22 17:32:55,486 - __main__ - INFO -   Backup LLM: process_healthy (Available: True)
2025-06-22 17:32:55,486 - __main__ - INFO -   Concurrent capacity: 2
2025-06-22 17:32:55,486 - __main__ - INFO - 
============================================================
2025-06-22 17:32:55,486 - __main__ - INFO - STARTING TEST SUITE: Concurrent Operations
2025-06-22 17:32:55,486 - __main__ - INFO - ============================================================
2025-06-22 17:32:55,487 - tests.test_concurrent_operations - INFO - Starting Concurrent Operations Tests
2025-06-22 17:32:55,487 - root - INFO - === STARTING TEST: Simultaneous Inference and Ingestion ===
2025-06-22 17:33:10,593 - tests.test_concurrent_operations - INFO - Inference 1 succeeded with 19 tokens
2025-06-22 17:33:10,594 - tests.test_concurrent_operations - INFO - Inference 2 succeeded with 119 tokens
2025-06-22 17:33:10,594 - tests.test_concurrent_operations - INFO - Inference 3 succeeded with 19 tokens
2025-06-22 17:33:10,594 - root - INFO - === TEST PASSED: Simultaneous Inference and Ingestion (Duration: 15.11s) ===
2025-06-22 17:33:10,594 - root - INFO - === COMPLETED TEST: Simultaneous Inference and Ingestion ===

2025-06-22 17:33:12,608 - root - INFO - === STARTING TEST: Multiple Inference with Unconsumed Streams ===
2025-06-22 17:33:15,572 - tests.test_concurrent_operations - INFO - Request 0 started successfully, got 3 tokens
2025-06-22 17:33:20,594 - root - INFO - === TEST FAILED: Multiple Inference with Unconsumed Streams (Duration: 7.99s) ===
2025-06-22 17:33:20,595 - root - ERROR - Error: Only 1/4 requests succeeded
2025-06-22 17:33:20,595 - root - INFO - === COMPLETED TEST: Multiple Inference with Unconsumed Streams ===

2025-06-22 17:33:22,598 - root - INFO - === STARTING TEST: Mixed Workload Handling ===
2025-06-22 17:33:24,983 - tests.test_concurrent_operations - INFO - Ingestion task 2 succeeded
2025-06-22 17:33:24,983 - tests.test_concurrent_operations - INFO - Ingestion task 3 succeeded
2025-06-22 17:33:24,984 - root - INFO - === TEST FAILED: Mixed Workload Handling (Duration: 2.39s) ===
2025-06-22 17:33:24,984 - root - ERROR - Error: Inference: 0/3, Ingestion: 2/2
2025-06-22 17:33:24,984 - root - INFO - === COMPLETED TEST: Mixed Workload Handling ===

2025-06-22 17:33:26,983 - __main__ - INFO - 
TEST SUITE COMPLETED: Concurrent Operations
2025-06-22 17:33:26,983 - __main__ - INFO -   Tests: 1/3 passed (33.3%)
2025-06-22 17:33:26,984 - __main__ - INFO -   Duration: 31.50s
2025-06-22 17:33:26,984 - __main__ - WARNING -   FAILURES in Concurrent Operations:
2025-06-22 17:33:26,984 - __main__ - WARNING -     - Multiple Inference with Unconsumed Streams: Only 1/4 requests succeeded
2025-06-22 17:33:26,984 - __main__ - WARNING -     - Mixed Workload Handling: Inference: 0/3, Ingestion: 2/2
2025-06-22 17:33:26,984 - __main__ - INFO - Pausing 10 seconds before next test suite...
2025-06-22 17:33:36,988 - __main__ - INFO - 
Checking server health before Data Isolation and Security...
2025-06-22 17:33:36,988 - __main__ - INFO - Checking server health before starting tests...
2025-06-22 17:33:36,994 - __main__ - INFO - Server health check passed:
2025-06-22 17:33:36,994 - __main__ - INFO -   Main LLM: process_healthy (Available: True)
2025-06-22 17:33:36,994 - __main__ - INFO -   Backup LLM: process_healthy (Available: True)
2025-06-22 17:33:36,994 - __main__ - INFO -   Concurrent capacity: 2
2025-06-22 17:33:36,995 - __main__ - INFO - 
============================================================
2025-06-22 17:33:36,995 - __main__ - INFO - STARTING TEST SUITE: Data Isolation and Security
2025-06-22 17:33:36,995 - __main__ - INFO - ============================================================
2025-06-22 17:33:36,995 - tests.test_data_isolation - INFO - Starting Data Isolation Tests
2025-06-22 17:33:36,995 - root - INFO - === STARTING TEST: User Request Isolation ===
2025-06-22 17:33:42,178 - tests.test_data_isolation - INFO - User user_b response: ...
2025-06-22 17:33:42,179 - tests.test_data_isolation - INFO - User user_c response: ...
2025-06-22 17:33:42,180 - root - INFO - === TEST FAILED: User Request Isolation (Duration: 5.18s) ===
2025-06-22 17:33:42,180 - root - ERROR - Error: Isolation failures: User user_a got status 500; User user_b didn't get their secret 'banana'; User user_c didn't get their secret 'cherry'
2025-06-22 17:33:42,180 - root - INFO - === COMPLETED TEST: User Request Isolation ===

2025-06-22 17:33:45,183 - root - INFO - === STARTING TEST: Concurrent Session Isolation ===
2025-06-22 17:33:50,244 - tests.test_data_isolation - INFO - Starting isolation test round 1
2025-06-22 17:33:50,510 - root - INFO - === TEST FAILED: Concurrent Session Isolation (Duration: 5.33s) ===
2025-06-22 17:33:50,511 - root - ERROR - Error: Session session_0 round 0 got status 500
2025-06-22 17:33:50,511 - root - INFO - === COMPLETED TEST: Concurrent Session Isolation ===

2025-06-22 17:33:53,523 - root - INFO - === STARTING TEST: Multi-User Concurrent Isolation ===
2025-06-22 17:34:03,738 - tests.test_data_isolation - INFO - Executing 20 concurrent queries for isolation test
2025-06-22 17:34:05,040 - root - INFO - === TEST FAILED: Multi-User Concurrent Isolation (Duration: 11.51s) ===
2025-06-22 17:34:05,040 - root - ERROR - Error: Isolation violations: User user_00 missing own sensitive info; User user_01 missing own sensitive info; User user_02 missing own sensitive info; User user_03 missing own sensitive info; User user_04 missing own sensitive info...
2025-06-22 17:34:05,040 - root - INFO - === COMPLETED TEST: Multi-User Concurrent Isolation ===

2025-06-22 17:34:08,053 - __main__ - INFO - 
TEST SUITE COMPLETED: Data Isolation and Security
2025-06-22 17:34:08,053 - __main__ - INFO -   Tests: 0/3 passed (0.0%)
2025-06-22 17:34:08,053 - __main__ - INFO -   Duration: 31.06s
2025-06-22 17:34:08,054 - __main__ - WARNING -   FAILURES in Data Isolation and Security:
2025-06-22 17:34:08,054 - __main__ - WARNING -     - User Request Isolation: Isolation failures: User user_a got status 500; User user_b didn't get their secret 'banana'; User user_c didn't get their secret 'cherry'
2025-06-22 17:34:08,054 - __main__ - WARNING -     - Concurrent Session Isolation: Session session_0 round 0 got status 500
2025-06-22 17:34:08,055 - __main__ - WARNING -     - Multi-User Concurrent Isolation: Isolation violations: User user_00 missing own sensitive info; User user_01 missing own sensitive info; User user_02 missing own sensitive info; User user_03 missing own sensitive info; User user_04 missing own sensitive info...
2025-06-22 17:34:08,055 - __main__ - INFO - Pausing 10 seconds before next test suite...
2025-06-22 17:34:18,065 - __main__ - INFO - 
Checking server health before Thread Safety...
2025-06-22 17:34:18,066 - __main__ - INFO - Checking server health before starting tests...
2025-06-22 17:34:18,072 - __main__ - INFO - Server health check passed:
2025-06-22 17:34:18,073 - __main__ - INFO -   Main LLM: process_healthy (Available: True)
2025-06-22 17:34:18,073 - __main__ - INFO -   Backup LLM: process_healthy (Available: True)
2025-06-22 17:34:18,073 - __main__ - INFO -   Concurrent capacity: 2
2025-06-22 17:34:18,074 - __main__ - INFO - 
============================================================
2025-06-22 17:34:18,074 - __main__ - INFO - STARTING TEST SUITE: Thread Safety
2025-06-22 17:34:18,074 - __main__ - INFO - ============================================================
2025-06-22 17:34:18,075 - tests.test_thread_safety - INFO - Starting Thread Safety Tests
2025-06-22 17:34:18,075 - root - INFO - === STARTING TEST: LLM Instance Locking ===
2025-06-22 17:34:21,102 - tests.test_thread_safety - INFO - Starting 6 concurrent requests to test locking
2025-06-22 17:34:21,597 - tests.test_thread_safety - ERROR - Request 0 failed with status 200
2025-06-22 17:34:21,597 - tests.test_thread_safety - ERROR - Request 1 failed with status 200
2025-06-22 17:34:21,597 - tests.test_thread_safety - ERROR - Request 2 failed with status 200
2025-06-22 17:34:21,597 - tests.test_thread_safety - ERROR - Request 3 failed with status 200
2025-06-22 17:34:21,597 - tests.test_thread_safety - ERROR - Request 4 failed with status 200
2025-06-22 17:34:21,597 - tests.test_thread_safety - ERROR - Request 5 failed with status 200
2025-06-22 17:34:21,598 - root - INFO - === TEST PASSED: LLM Instance Locking (Duration: 3.52s) ===
2025-06-22 17:34:21,598 - root - INFO - === COMPLETED TEST: LLM Instance Locking ===

2025-06-22 17:34:26,607 - root - INFO - === STARTING TEST: Concurrent Access Prevention ===
2025-06-22 17:34:29,627 - tests.test_thread_safety - INFO - Starting wave 1 with 8 rapid requests
2025-06-22 17:34:32,261 - tests.test_thread_safety - INFO - Starting wave 2 with 8 rapid requests
2025-06-22 17:34:34,820 - tests.test_thread_safety - INFO - Starting wave 3 with 8 rapid requests
2025-06-22 17:34:37,400 - tests.test_thread_safety - INFO - Concurrent access test results: 0/24 successful
2025-06-22 17:34:37,401 - tests.test_thread_safety - INFO - Thread safety violations: 0
2025-06-22 17:34:37,403 - root - INFO - === TEST PASSED: Concurrent Access Prevention (Duration: 10.79s) ===
2025-06-22 17:34:37,404 - root - INFO - === COMPLETED TEST: Concurrent Access Prevention ===

2025-06-22 17:34:42,407 - root - INFO - === STARTING TEST: GGML_ASSERT Prevention ===
2025-06-22 17:34:45,426 - tests.test_thread_safety - INFO - Testing pattern: burst
2025-06-22 17:34:46,140 - tests.test_thread_safety - INFO - Pattern burst: 0/10 successful, 0 GGML errors
2025-06-22 17:34:47,149 - tests.test_thread_safety - INFO - Testing pattern: overlapping
2025-06-22 17:34:48,177 - tests.test_thread_safety - INFO - Pattern overlapping: 0/6 successful, 0 GGML errors
2025-06-22 17:34:49,190 - tests.test_thread_safety - INFO - Testing pattern: rapid_sequential
2025-06-22 17:34:50,345 - tests.test_thread_safety - INFO - Pattern rapid_sequential: 0/8 successful, 0 GGML errors
2025-06-22 17:34:51,350 - root - INFO - === TEST PASSED: GGML_ASSERT Prevention (Duration: 8.94s) ===
2025-06-22 17:34:51,351 - root - INFO - === COMPLETED TEST: GGML_ASSERT Prevention ===

2025-06-22 17:34:56,351 - __main__ - INFO - 
TEST SUITE COMPLETED: Thread Safety
2025-06-22 17:34:56,351 - __main__ - INFO -   Tests: 3/3 passed (100.0%)
2025-06-22 17:34:56,351 - __main__ - INFO -   Duration: 38.28s
2025-06-22 17:34:56,352 - __main__ - INFO - Pausing 10 seconds before next test suite...
2025-06-22 17:35:06,357 - __main__ - INFO - 
Checking server health before Error Recovery and Resilience...
2025-06-22 17:35:06,358 - __main__ - INFO - Checking server health before starting tests...
2025-06-22 17:35:06,362 - __main__ - INFO - Server health check passed:
2025-06-22 17:35:06,362 - __main__ - INFO -   Main LLM: process_healthy (Available: True)
2025-06-22 17:35:06,362 - __main__ - INFO -   Backup LLM: process_healthy (Available: True)
2025-06-22 17:35:06,363 - __main__ - INFO -   Concurrent capacity: 2
2025-06-22 17:35:06,363 - __main__ - INFO - 
============================================================
2025-06-22 17:35:06,363 - __main__ - INFO - STARTING TEST SUITE: Error Recovery and Resilience
2025-06-22 17:35:06,363 - __main__ - INFO - ============================================================
2025-06-22 17:35:06,363 - tests.test_error_recovery - INFO - Starting Error Recovery and Resilience Tests
2025-06-22 17:35:06,364 - root - INFO - === STARTING TEST: LLM Process Health Monitoring ===
2025-06-22 17:35:06,368 - tests.test_error_recovery - INFO - Initial status - Main: process_healthy, Backup: process_healthy
2025-06-22 17:35:08,370 - tests.test_error_recovery - INFO - Health check 1: Main=process_healthy, Backup=process_healthy
2025-06-22 17:35:10,382 - tests.test_error_recovery - INFO - Health check 2: Main=process_healthy, Backup=process_healthy
2025-06-22 17:35:12,395 - tests.test_error_recovery - INFO - Health check 3: Main=process_healthy, Backup=process_healthy
2025-06-22 17:35:14,405 - tests.test_error_recovery - INFO - Health check 4: Main=process_healthy, Backup=process_healthy
2025-06-22 17:35:16,421 - tests.test_error_recovery - INFO - Health check 5: Main=process_healthy, Backup=process_healthy
2025-06-22 17:35:16,422 - root - INFO - === TEST PASSED: LLM Process Health Monitoring (Duration: 10.06s) ===
2025-06-22 17:35:16,422 - root - INFO - === COMPLETED TEST: LLM Process Health Monitoring ===

2025-06-22 17:35:21,428 - root - INFO - === STARTING TEST: Graceful Degradation ===
2025-06-22 17:35:24,444 - tests.test_error_recovery - INFO - Testing normal operation with both LLMs
2025-06-22 17:35:24,619 - tests.test_error_recovery - INFO - Normal operation: 3/3 requests succeeded
2025-06-22 17:35:24,619 - tests.test_error_recovery - INFO - Testing under high load (simulating degraded conditions)
2025-06-22 17:35:25,278 - tests.test_error_recovery - INFO - Stress test: 10/10 requests succeeded
2025-06-22 17:35:25,279 - tests.test_error_recovery - INFO - Health after stress: True
2025-06-22 17:35:25,279 - root - INFO - === TEST PASSED: Graceful Degradation (Duration: 3.85s) ===
2025-06-22 17:35:25,280 - root - INFO - === COMPLETED TEST: Graceful Degradation ===

2025-06-22 17:35:30,284 - root - INFO - === STARTING TEST: System Recovery After Errors ===
2025-06-22 17:35:33,301 - tests.test_error_recovery - INFO - Testing recovery scenario: invalid_collection
2025-06-22 17:35:33,425 - tests.test_error_recovery - INFO - Scenario invalid_collection: status=200, tokens=0
2025-06-22 17:35:34,429 - tests.test_error_recovery - INFO - Testing recovery scenario: malformed_query
2025-06-22 17:35:34,435 - tests.test_error_recovery - INFO - Scenario malformed_query: status=200, tokens=0
2025-06-22 17:35:35,436 - tests.test_error_recovery - INFO - Testing recovery scenario: very_long_query
2025-06-22 17:35:37,035 - tests.test_error_recovery - INFO - Scenario very_long_query: status=200, tokens=0
2025-06-22 17:35:38,047 - tests.test_error_recovery - INFO - Testing system functionality after error scenarios
2025-06-22 17:35:41,125 - root - INFO - === TEST FAILED: System Recovery After Errors (Duration: 10.84s) ===
2025-06-22 17:35:41,125 - root - ERROR - Error: Normal operation failed after error scenarios: status=200, tokens=0
2025-06-22 17:35:41,125 - root - INFO - === COMPLETED TEST: System Recovery After Errors ===

2025-06-22 17:35:46,137 - root - INFO - === STARTING TEST: Request Timeout Handling ===
2025-06-22 17:35:46,137 - root - INFO - === TEST FAILED: Request Timeout Handling (Duration: 0.00s) ===
2025-06-22 17:35:46,138 - root - ERROR - Error: name 'aiohttp' is not defined
2025-06-22 17:35:46,138 - root - INFO - === COMPLETED TEST: Request Timeout Handling ===

2025-06-22 17:35:51,148 - __main__ - INFO - 
TEST SUITE COMPLETED: Error Recovery and Resilience
2025-06-22 17:35:51,148 - __main__ - INFO -   Tests: 2/4 passed (50.0%)
2025-06-22 17:35:51,148 - __main__ - INFO -   Duration: 44.79s
2025-06-22 17:35:51,149 - __main__ - WARNING -   FAILURES in Error Recovery and Resilience:
2025-06-22 17:35:51,149 - __main__ - WARNING -     - System Recovery After Errors: Normal operation failed after error scenarios: status=200, tokens=0
2025-06-22 17:35:51,149 - __main__ - WARNING -     - Request Timeout Handling: name 'aiohttp' is not defined
2025-06-22 17:35:51,149 - __main__ - INFO - Pausing 10 seconds before next test suite...
2025-06-22 17:36:01,163 - __main__ - INFO - 
Checking server health before Streaming Response...
2025-06-22 17:36:01,163 - __main__ - INFO - Checking server health before starting tests...
2025-06-22 17:36:01,169 - __main__ - INFO - Server health check passed:
2025-06-22 17:36:01,170 - __main__ - INFO -   Main LLM: process_healthy (Available: True)
2025-06-22 17:36:01,170 - __main__ - INFO -   Backup LLM: process_healthy (Available: True)
2025-06-22 17:36:01,170 - __main__ - INFO -   Concurrent capacity: 2
2025-06-22 17:36:01,171 - __main__ - INFO - 
============================================================
2025-06-22 17:36:01,171 - __main__ - INFO - STARTING TEST SUITE: Streaming Response
2025-06-22 17:36:01,171 - __main__ - INFO - ============================================================
2025-06-22 17:36:01,171 - tests.test_streaming - INFO - Starting Streaming Response Tests
2025-06-22 17:36:01,172 - root - INFO - === STARTING TEST: Real-time Token Streaming ===
2025-06-22 17:36:04,193 - tests.test_streaming - INFO - Testing streaming with query 1: What is this about?...
2025-06-22 17:36:04,274 - tests.test_streaming - INFO - Query 1 results:
2025-06-22 17:36:04,275 - tests.test_streaming - INFO -   Tokens: 0
2025-06-22 17:36:04,275 - root - INFO - === TEST FAILED: Real-time Token Streaming (Duration: 3.10s) ===
2025-06-22 17:36:04,275 - root - ERROR - Error: unsupported format string passed to NoneType.__format__
2025-06-22 17:36:04,275 - root - INFO - === COMPLETED TEST: Real-time Token Streaming ===

2025-06-22 17:36:07,289 - root - INFO - === STARTING TEST: Concurrent Streaming Performance ===
2025-06-22 17:36:10,306 - tests.test_streaming - INFO - Starting 8 concurrent streaming requests
2025-06-22 17:36:10,822 - tests.test_streaming - INFO - Concurrent streaming results:
2025-06-22 17:36:10,822 - tests.test_streaming - INFO -   Successful: 8/8
2025-06-22 17:36:10,822 - tests.test_streaming - INFO -   Failed: 0
2025-06-22 17:36:10,824 - tests.test_streaming - INFO -   Avg time to first token: 0.000s
2025-06-22 17:36:10,824 - tests.test_streaming - INFO -   Avg tokens per second: 0.00
2025-06-22 17:36:10,824 - tests.test_streaming - INFO -   Total tokens: 0
2025-06-22 17:36:10,825 - root - INFO - === TEST FAILED: Concurrent Streaming Performance (Duration: 3.53s) ===
2025-06-22 17:36:10,825 - root - ERROR - Error: Concurrent streaming failed: success_rate=100.00%, first_token=0.000s, tokens/sec=0.00
2025-06-22 17:36:10,825 - root - INFO - === COMPLETED TEST: Concurrent Streaming Performance ===

2025-06-22 17:36:13,839 - root - INFO - === STARTING TEST: No Buffering Delays ===
2025-06-22 17:36:16,861 - tests.test_streaming - INFO - Testing buffering with query 1
2025-06-22 17:36:16,922 - tests.test_streaming - INFO - Query 1: 0 tokens, avg interval: 0.000s, max interval: 0.000s
2025-06-22 17:36:17,928 - tests.test_streaming - INFO - Testing buffering with query 2
2025-06-22 17:36:18,004 - tests.test_streaming - INFO - Query 2: 0 tokens, avg interval: 0.000s, max interval: 0.000s
2025-06-22 17:36:19,013 - tests.test_streaming - INFO - Testing buffering with query 3
2025-06-22 17:36:19,094 - tests.test_streaming - INFO - Query 3: 0 tokens, avg interval: 0.000s, max interval: 0.000s
2025-06-22 17:36:20,096 - root - INFO - === TEST FAILED: No Buffering Delays (Duration: 6.26s) ===
2025-06-22 17:36:20,097 - root - ERROR - Error: No token intervals measured
2025-06-22 17:36:20,097 - root - INFO - === COMPLETED TEST: No Buffering Delays ===

2025-06-22 17:36:23,112 - __main__ - INFO - 
TEST SUITE COMPLETED: Streaming Response
2025-06-22 17:36:23,113 - __main__ - INFO -   Tests: 0/3 passed (0.0%)
2025-06-22 17:36:23,113 - __main__ - INFO -   Duration: 21.94s
2025-06-22 17:36:23,114 - __main__ - WARNING -   FAILURES in Streaming Response:
2025-06-22 17:36:23,114 - __main__ - WARNING -     - Real-time Token Streaming: unsupported format string passed to NoneType.__format__
2025-06-22 17:36:23,114 - __main__ - WARNING -     - Concurrent Streaming Performance: Concurrent streaming failed: success_rate=100.00%, first_token=0.000s, tokens/sec=0.00
2025-06-22 17:36:23,114 - __main__ - WARNING -     - No Buffering Delays: No token intervals measured
2025-06-22 17:36:23,115 - __main__ - INFO - Pausing 10 seconds before next test suite...
2025-06-22 17:36:33,127 - __main__ - INFO - 
Checking server health before Scalability and Stress...
2025-06-22 17:36:33,128 - __main__ - INFO - Checking server health before starting tests...
2025-06-22 17:36:33,134 - __main__ - INFO - Server health check passed:
2025-06-22 17:36:33,134 - __main__ - INFO -   Main LLM: process_healthy (Available: True)
2025-06-22 17:36:33,134 - __main__ - INFO -   Backup LLM: process_healthy (Available: True)
2025-06-22 17:36:33,134 - __main__ - INFO -   Concurrent capacity: 2
2025-06-22 17:36:33,135 - __main__ - INFO - 
============================================================
2025-06-22 17:36:33,135 - __main__ - INFO - STARTING TEST SUITE: Scalability and Stress
2025-06-22 17:36:33,136 - __main__ - INFO - ============================================================
2025-06-22 17:36:33,136 - tests.test_scalability_stress - INFO - Starting Scalability and Stress Tests
2025-06-22 17:36:33,136 - root - INFO - === STARTING TEST: Moderate Concurrent Load (100 requests) ===
2025-06-22 17:36:36,161 - tests.test_scalability_stress - INFO - Starting 100 concurrent requests...
2025-06-22 17:36:41,924 - tests.test_scalability_stress - ERROR - Request 0 failed with status 200
2025-06-22 17:36:41,924 - tests.test_scalability_stress - ERROR - Request 1 failed with status 200
2025-06-22 17:36:41,925 - tests.test_scalability_stress - ERROR - Request 2 failed with status 200
2025-06-22 17:36:41,925 - tests.test_scalability_stress - ERROR - Request 3 failed with status 200
2025-06-22 17:36:41,925 - tests.test_scalability_stress - ERROR - Request 4 failed with status 200
2025-06-22 17:36:41,925 - tests.test_scalability_stress - ERROR - Request 5 failed with status 200
2025-06-22 17:36:41,926 - tests.test_scalability_stress - ERROR - Request 6 failed with status 200
2025-06-22 17:36:41,926 - tests.test_scalability_stress - ERROR - Request 7 failed with status 200
2025-06-22 17:36:41,926 - tests.test_scalability_stress - ERROR - Request 8 failed with status 200
2025-06-22 17:36:41,926 - tests.test_scalability_stress - ERROR - Request 9 failed with status 200
2025-06-22 17:36:41,926 - tests.test_scalability_stress - ERROR - Request 10 failed with status 200
2025-06-22 17:36:41,926 - tests.test_scalability_stress - ERROR - Request 11 failed with status 200
2025-06-22 17:36:41,926 - tests.test_scalability_stress - ERROR - Request 12 failed with status 200
2025-06-22 17:36:41,927 - tests.test_scalability_stress - ERROR - Request 13 failed with status 200
2025-06-22 17:36:41,927 - tests.test_scalability_stress - ERROR - Request 14 failed with status 200
2025-06-22 17:36:41,927 - tests.test_scalability_stress - ERROR - Request 15 failed with status 200
2025-06-22 17:36:41,927 - tests.test_scalability_stress - ERROR - Request 16 failed with status 200
2025-06-22 17:36:41,927 - tests.test_scalability_stress - ERROR - Request 17 failed with status 200
2025-06-22 17:36:41,927 - tests.test_scalability_stress - ERROR - Request 18 failed with status 200
2025-06-22 17:36:41,927 - tests.test_scalability_stress - ERROR - Request 19 failed with status 200
2025-06-22 17:36:41,927 - tests.test_scalability_stress - ERROR - Request 20 failed with status 200
2025-06-22 17:36:41,928 - tests.test_scalability_stress - ERROR - Request 21 failed with status 200
2025-06-22 17:36:41,928 - tests.test_scalability_stress - ERROR - Request 22 failed with status 200
2025-06-22 17:36:41,928 - tests.test_scalability_stress - ERROR - Request 23 failed with status 200
2025-06-22 17:36:41,928 - tests.test_scalability_stress - ERROR - Request 24 failed with status 200
2025-06-22 17:36:41,928 - tests.test_scalability_stress - ERROR - Request 25 failed with status 200
2025-06-22 17:36:41,928 - tests.test_scalability_stress - ERROR - Request 26 failed with status 200
2025-06-22 17:36:41,928 - tests.test_scalability_stress - ERROR - Request 27 failed with status 200
2025-06-22 17:36:41,928 - tests.test_scalability_stress - ERROR - Request 28 failed with status 200
2025-06-22 17:36:41,928 - tests.test_scalability_stress - ERROR - Request 29 failed with status 200
2025-06-22 17:36:41,929 - tests.test_scalability_stress - ERROR - Request 30 failed with status 200
2025-06-22 17:36:41,929 - tests.test_scalability_stress - ERROR - Request 31 failed with status 200
2025-06-22 17:36:41,929 - tests.test_scalability_stress - ERROR - Request 32 failed with status 200
2025-06-22 17:36:41,929 - tests.test_scalability_stress - ERROR - Request 33 failed with status 200
2025-06-22 17:36:41,929 - tests.test_scalability_stress - ERROR - Request 34 failed with status 200
2025-06-22 17:36:41,929 - tests.test_scalability_stress - ERROR - Request 35 failed with status 200
2025-06-22 17:36:41,929 - tests.test_scalability_stress - ERROR - Request 36 failed with status 200
2025-06-22 17:36:41,929 - tests.test_scalability_stress - ERROR - Request 37 failed with status 200
2025-06-22 17:36:41,930 - tests.test_scalability_stress - ERROR - Request 38 failed with status 200
2025-06-22 17:36:41,930 - tests.test_scalability_stress - ERROR - Request 39 failed with status 200
2025-06-22 17:36:41,930 - tests.test_scalability_stress - ERROR - Request 40 failed with status 200
2025-06-22 17:36:41,930 - tests.test_scalability_stress - ERROR - Request 41 failed with status 200
2025-06-22 17:36:41,930 - tests.test_scalability_stress - ERROR - Request 42 failed with status 200
2025-06-22 17:36:41,930 - tests.test_scalability_stress - ERROR - Request 43 failed with status 200
2025-06-22 17:36:41,930 - tests.test_scalability_stress - ERROR - Request 44 failed with status 200
2025-06-22 17:36:41,930 - tests.test_scalability_stress - ERROR - Request 45 failed with status 200
2025-06-22 17:36:41,930 - tests.test_scalability_stress - ERROR - Request 46 failed with status 200
2025-06-22 17:36:41,931 - tests.test_scalability_stress - ERROR - Request 47 failed with status 200
2025-06-22 17:36:41,931 - tests.test_scalability_stress - ERROR - Request 48 failed with status 200
2025-06-22 17:36:41,931 - tests.test_scalability_stress - ERROR - Request 49 failed with status 200
2025-06-22 17:36:41,931 - tests.test_scalability_stress - ERROR - Request 50 failed with status 200
2025-06-22 17:36:41,931 - tests.test_scalability_stress - ERROR - Request 51 failed with status 200
2025-06-22 17:36:41,931 - tests.test_scalability_stress - ERROR - Request 52 failed with status 200
2025-06-22 17:36:41,931 - tests.test_scalability_stress - ERROR - Request 53 failed with status 200
2025-06-22 17:36:41,931 - tests.test_scalability_stress - ERROR - Request 54 failed with status 200
2025-06-22 17:36:41,931 - tests.test_scalability_stress - ERROR - Request 55 failed with status 200
2025-06-22 17:36:41,932 - tests.test_scalability_stress - ERROR - Request 56 failed with status 200
2025-06-22 17:36:41,932 - tests.test_scalability_stress - ERROR - Request 57 failed with status 200
2025-06-22 17:36:41,932 - tests.test_scalability_stress - ERROR - Request 58 failed with status 200
2025-06-22 17:36:41,932 - tests.test_scalability_stress - ERROR - Request 59 failed with status 200
2025-06-22 17:36:41,932 - tests.test_scalability_stress - ERROR - Request 60 failed with status 200
2025-06-22 17:36:41,932 - tests.test_scalability_stress - ERROR - Request 61 failed with status 200
2025-06-22 17:36:41,932 - tests.test_scalability_stress - ERROR - Request 62 failed with status 200
2025-06-22 17:36:41,933 - tests.test_scalability_stress - ERROR - Request 63 failed with status 200
2025-06-22 17:36:41,933 - tests.test_scalability_stress - ERROR - Request 64 failed with status 200
2025-06-22 17:36:41,933 - tests.test_scalability_stress - ERROR - Request 65 failed with status 200
2025-06-22 17:36:41,933 - tests.test_scalability_stress - ERROR - Request 66 failed with status 200
2025-06-22 17:36:41,933 - tests.test_scalability_stress - ERROR - Request 67 failed with status 200
2025-06-22 17:36:41,933 - tests.test_scalability_stress - ERROR - Request 68 failed with status 200
2025-06-22 17:36:41,933 - tests.test_scalability_stress - ERROR - Request 69 failed with status 200
2025-06-22 17:36:41,933 - tests.test_scalability_stress - ERROR - Request 70 failed with status 200
2025-06-22 17:36:41,934 - tests.test_scalability_stress - ERROR - Request 71 failed with status 200
2025-06-22 17:36:41,934 - tests.test_scalability_stress - ERROR - Request 72 failed with status 200
2025-06-22 17:36:41,934 - tests.test_scalability_stress - ERROR - Request 73 failed with status 200
2025-06-22 17:36:41,934 - tests.test_scalability_stress - ERROR - Request 74 failed with status 200
2025-06-22 17:36:41,934 - tests.test_scalability_stress - ERROR - Request 75 failed with status 200
2025-06-22 17:36:41,934 - tests.test_scalability_stress - ERROR - Request 76 failed with status 200
2025-06-22 17:36:41,934 - tests.test_scalability_stress - ERROR - Request 77 failed with status 200
2025-06-22 17:36:41,935 - tests.test_scalability_stress - ERROR - Request 78 failed with status 200
2025-06-22 17:36:41,935 - tests.test_scalability_stress - ERROR - Request 79 failed with status 200
2025-06-22 17:36:41,935 - tests.test_scalability_stress - ERROR - Request 80 failed with status 200
2025-06-22 17:36:41,935 - tests.test_scalability_stress - ERROR - Request 81 failed with status 200
2025-06-22 17:36:41,935 - tests.test_scalability_stress - ERROR - Request 82 failed with status 200
2025-06-22 17:36:41,935 - tests.test_scalability_stress - ERROR - Request 83 failed with status 200
2025-06-22 17:36:41,935 - tests.test_scalability_stress - ERROR - Request 84 failed with status 200
2025-06-22 17:36:41,935 - tests.test_scalability_stress - ERROR - Request 85 failed with status 200
2025-06-22 17:36:41,936 - tests.test_scalability_stress - ERROR - Request 86 failed with status 200
2025-06-22 17:36:41,936 - tests.test_scalability_stress - ERROR - Request 87 failed with status 200
2025-06-22 17:36:41,936 - tests.test_scalability_stress - ERROR - Request 88 failed with status 200
2025-06-22 17:36:41,936 - tests.test_scalability_stress - ERROR - Request 89 failed with status 200
2025-06-22 17:36:41,936 - tests.test_scalability_stress - ERROR - Request 90 failed with status 200
2025-06-22 17:36:41,936 - tests.test_scalability_stress - ERROR - Request 91 failed with status 200
2025-06-22 17:36:41,936 - tests.test_scalability_stress - ERROR - Request 92 failed with status 200
2025-06-22 17:36:41,937 - tests.test_scalability_stress - ERROR - Request 93 failed with status 200
2025-06-22 17:36:41,937 - tests.test_scalability_stress - ERROR - Request 94 failed with status 200
2025-06-22 17:36:41,937 - tests.test_scalability_stress - ERROR - Request 95 failed with status 200
2025-06-22 17:36:41,937 - tests.test_scalability_stress - ERROR - Request 96 failed with status 200
2025-06-22 17:36:41,937 - tests.test_scalability_stress - ERROR - Request 97 failed with status 200
2025-06-22 17:36:41,937 - tests.test_scalability_stress - ERROR - Request 98 failed with status 200
2025-06-22 17:36:41,938 - tests.test_scalability_stress - ERROR - Request 99 failed with status 200
2025-06-22 17:36:41,938 - tests.test_scalability_stress - INFO - Results: 0 success, 100 errors, 0 timeouts
2025-06-22 17:36:41,938 - tests.test_scalability_stress - INFO - Response times - Avg: 5.77s, Median: 5.77s, Max: 5.78s
2025-06-22 17:36:41,946 - root - INFO - === TEST FAILED: Moderate Concurrent Load (100 requests) (Duration: 8.80s) ===
2025-06-22 17:36:41,947 - root - ERROR - Error: Success rate: 0.00%, Avg response time: 5.77s
2025-06-22 17:36:41,947 - root - INFO - === COMPLETED TEST: Moderate Concurrent Load (100 requests) ===

2025-06-22 17:36:51,955 - root - INFO - === STARTING TEST: High Concurrent Load (500 requests) ===
2025-06-22 17:36:54,974 - tests.test_scalability_stress - INFO - Starting batch 1: requests 0-49
2025-06-22 17:36:58,915 - tests.test_scalability_stress - INFO - Starting batch 2: requests 50-99
2025-06-22 17:37:03,068 - tests.test_scalability_stress - INFO - Starting batch 3: requests 100-149
2025-06-22 17:37:06,793 - tests.test_scalability_stress - INFO - Starting batch 4: requests 150-199
2025-06-22 17:37:10,679 - tests.test_scalability_stress - INFO - Starting batch 5: requests 200-249
2025-06-22 17:37:14,536 - tests.test_scalability_stress - INFO - Starting batch 6: requests 250-299
2025-06-22 17:37:18,247 - tests.test_scalability_stress - INFO - Starting batch 7: requests 300-349
2025-06-22 17:37:22,136 - tests.test_scalability_stress - INFO - Starting batch 8: requests 350-399
2025-06-22 17:37:26,086 - tests.test_scalability_stress - INFO - Starting batch 9: requests 400-449
2025-06-22 17:37:29,938 - tests.test_scalability_stress - INFO - Starting batch 10: requests 450-499
2025-06-22 17:37:33,723 - tests.test_scalability_stress - INFO - Results: 0 success, 500 errors, 0 timeouts
2025-06-22 17:37:33,723 - tests.test_scalability_stress - INFO - Response times - Avg: 21.18s, Median: 21.11s, 95th: 38.75s, Max: 38.75s
2025-06-22 17:37:33,727 - root - INFO - === TEST FAILED: High Concurrent Load (500 requests) (Duration: 41.77s) ===
2025-06-22 17:37:33,727 - root - ERROR - Error: Success rate: 0.00%, Avg response time: 21.18s
2025-06-22 17:37:33,728 - root - INFO - === COMPLETED TEST: High Concurrent Load (500 requests) ===

2025-06-22 17:37:43,732 - root - INFO - === STARTING TEST: Extreme Load (1000 requests) ===
2025-06-22 17:37:48,749 - tests.test_scalability_stress - INFO - Starting extreme load test with 1000 requests in batches of 25
2025-06-22 17:37:48,749 - tests.test_scalability_stress - INFO - Progress: 0/1000 requests started
2025-06-22 17:37:59,750 - tests.test_scalability_stress - INFO - Progress: 100/1000 requests started
2025-06-22 17:38:23,264 - tests.test_scalability_stress - INFO - Progress: 200/1000 requests started
2025-06-22 17:38:31,416 - tests.test_scalability_stress - INFO - Progress: 300/1000 requests started
2025-06-22 17:38:39,934 - tests.test_scalability_stress - INFO - Progress: 400/1000 requests started
2025-06-22 17:38:48,257 - tests.test_scalability_stress - INFO - Progress: 500/1000 requests started
2025-06-22 17:38:56,513 - tests.test_scalability_stress - INFO - Progress: 600/1000 requests started
2025-06-22 17:39:04,693 - tests.test_scalability_stress - INFO - Progress: 700/1000 requests started
2025-06-22 17:39:13,048 - tests.test_scalability_stress - INFO - Progress: 800/1000 requests started
2025-06-22 17:39:21,494 - tests.test_scalability_stress - INFO - Progress: 900/1000 requests started
2025-06-22 17:39:29,607 - tests.test_scalability_stress - INFO - EXTREME LOAD RESULTS:
2025-06-22 17:39:29,608 - tests.test_scalability_stress - INFO -   Success: 0, Errors: 1000, Timeouts: 0
2025-06-22 17:39:29,608 - tests.test_scalability_stress - INFO -   Response times - Avg: 44.55s, Median: 42.44s
2025-06-22 17:39:29,608 - tests.test_scalability_stress - INFO -   Response times - 95th: 98.59s, 99th: 100.86s, Max: 100.86s
2025-06-22 17:39:29,611 - root - INFO - === TEST FAILED: Extreme Load (1000 requests) (Duration: 105.88s) ===
2025-06-22 17:39:29,612 - root - ERROR - Error: Success rate too low: 0.00% (expected >70%)
2025-06-22 17:39:29,612 - root - INFO - === COMPLETED TEST: Extreme Load (1000 requests) ===

2025-06-22 17:39:39,624 - __main__ - INFO - 
TEST SUITE COMPLETED: Scalability and Stress
2025-06-22 17:39:39,624 - __main__ - INFO -   Tests: 0/3 passed (0.0%)
2025-06-22 17:39:39,624 - __main__ - INFO -   Duration: 186.49s
2025-06-22 17:39:39,624 - __main__ - WARNING -   FAILURES in Scalability and Stress:
2025-06-22 17:39:39,624 - __main__ - WARNING -     - Moderate Concurrent Load (100 requests): Success rate: 0.00%, Avg response time: 5.77s
2025-06-22 17:39:39,624 - __main__ - WARNING -     - High Concurrent Load (500 requests): Success rate: 0.00%, Avg response time: 21.18s
2025-06-22 17:39:39,624 - __main__ - WARNING -     - Extreme Load (1000 requests): Success rate too low: 0.00% (expected >70%)
2025-06-22 17:39:39,624 - __main__ - INFO - Pausing 10 seconds before next test suite...
2025-06-22 17:39:49,637 - __main__ - INFO - 
Final server health check...
2025-06-22 17:39:49,637 - __main__ - INFO - Checking server health before starting tests...
2025-06-22 17:39:49,642 - __main__ - INFO - Server health check passed:
2025-06-22 17:39:49,642 - __main__ - INFO -   Main LLM: process_healthy (Available: True)
2025-06-22 17:39:49,642 - __main__ - INFO -   Backup LLM: process_healthy (Available: True)
2025-06-22 17:39:49,642 - __main__ - INFO -   Concurrent capacity: 2
2025-06-22 17:39:49,643 - __main__ - INFO - 
================================================================================
2025-06-22 17:39:49,643 - __main__ - INFO - COMPREHENSIVE TEST RESULTS SUMMARY
2025-06-22 17:39:49,643 - __main__ - INFO - ================================================================================
2025-06-22 17:39:49,644 - __main__ - INFO - Test execution completed: 2025-06-22T17:39:49.***********-06-22 17:39:49,644 - __main__ - INFO - Total duration: 495.11 seconds
2025-06-22 17:39:49,644 - __main__ - INFO - Total tests executed: 22
2025-06-22 17:39:49,644 - __main__ - INFO - Overall success rate: 40.9%
2025-06-22 17:39:49,644 - __main__ - INFO - Final server health: HEALTHY
2025-06-22 17:39:49,645 - __main__ - INFO - 
RESULTS BY TEST SUITE:
2025-06-22 17:39:49,645 - __main__ - INFO - ------------------------------------------------------------
2025-06-22 17:39:49,645 - __main__ - INFO - Concurrent Request Handling    |  3/ 3 | 100.0% | PASS
2025-06-22 17:39:49,645 - __main__ - INFO - Concurrent Operations          |  1/ 3 |  33.3% | PARTIAL
2025-06-22 17:39:49,645 - __main__ - INFO - Data Isolation and Security    |  0/ 3 |   0.0% | FAIL
2025-06-22 17:39:49,645 - __main__ - INFO - Thread Safety                  |  3/ 3 | 100.0% | PASS
2025-06-22 17:39:49,646 - __main__ - INFO - Error Recovery and Resilience  |  2/ 4 |  50.0% | PARTIAL
2025-06-22 17:39:49,646 - __main__ - INFO - Streaming Response             |  0/ 3 |   0.0% | FAIL
2025-06-22 17:39:49,646 - __main__ - INFO - Scalability and Stress         |  0/ 3 |   0.0% | FAIL
2025-06-22 17:39:49,646 - __main__ - INFO - 
FAILED TESTS ANALYSIS (13 failures):
2025-06-22 17:39:49,646 - __main__ - INFO - ------------------------------------------------------------
2025-06-22 17:39:49,647 - __main__ - INFO - FAIL: Multiple Inference with Unconsumed Streams
2025-06-22 17:39:49,647 - __main__ - INFO -       Duration: 7.99s
2025-06-22 17:39:49,647 - __main__ - INFO -       Error: Only 1/4 requests succeeded
2025-06-22 17:39:49,647 - __main__ - INFO - 
2025-06-22 17:39:49,647 - __main__ - INFO - FAIL: Mixed Workload Handling
2025-06-22 17:39:49,647 - __main__ - INFO -       Duration: 2.39s
2025-06-22 17:39:49,648 - __main__ - INFO -       Error: Inference: 0/3, Ingestion: 2/2
2025-06-22 17:39:49,648 - __main__ - INFO - 
2025-06-22 17:39:49,648 - __main__ - INFO - FAIL: User Request Isolation
2025-06-22 17:39:49,648 - __main__ - INFO -       Duration: 5.18s
2025-06-22 17:39:49,648 - __main__ - INFO -       Error: Isolation failures: User user_a got status 500; User user_b didn't get their secret 'banana'; User user_c didn't get their secret 'cherry'
2025-06-22 17:39:49,649 - __main__ - INFO - 
2025-06-22 17:39:49,649 - __main__ - INFO - FAIL: Concurrent Session Isolation
2025-06-22 17:39:49,649 - __main__ - INFO -       Duration: 5.33s
2025-06-22 17:39:49,649 - __main__ - INFO -       Error: Session session_0 round 0 got status 500
2025-06-22 17:39:49,649 - __main__ - INFO - 
2025-06-22 17:39:49,649 - __main__ - INFO - FAIL: Multi-User Concurrent Isolation
2025-06-22 17:39:49,650 - __main__ - INFO -       Duration: 11.51s
2025-06-22 17:39:49,650 - __main__ - INFO -       Error: Isolation violations: User user_00 missing own sensitive info; User user_01 missing own sensitive info; User user_02 missing own sensitive info; User user_03 missing own sensitive info; User user_04 missing own sensitive info...
2025-06-22 17:39:49,650 - __main__ - INFO - 
2025-06-22 17:39:49,650 - __main__ - INFO - FAIL: System Recovery After Errors
2025-06-22 17:39:49,650 - __main__ - INFO -       Duration: 10.84s
2025-06-22 17:39:49,650 - __main__ - INFO -       Error: Normal operation failed after error scenarios: status=200, tokens=0
2025-06-22 17:39:49,651 - __main__ - INFO - 
2025-06-22 17:39:49,651 - __main__ - INFO - FAIL: Request Timeout Handling
2025-06-22 17:39:49,651 - __main__ - INFO -       Duration: 0.00s
2025-06-22 17:39:49,651 - __main__ - INFO -       Error: name 'aiohttp' is not defined
2025-06-22 17:39:49,651 - __main__ - INFO - 
2025-06-22 17:39:49,651 - __main__ - INFO - FAIL: Real-time Token Streaming
2025-06-22 17:39:49,652 - __main__ - INFO -       Duration: 3.10s
2025-06-22 17:39:49,652 - __main__ - INFO -       Error: unsupported format string passed to NoneType.__format__
2025-06-22 17:39:49,652 - __main__ - INFO - 
2025-06-22 17:39:49,652 - __main__ - INFO - FAIL: Concurrent Streaming Performance
2025-06-22 17:39:49,652 - __main__ - INFO -       Duration: 3.53s
2025-06-22 17:39:49,652 - __main__ - INFO -       Error: Concurrent streaming failed: success_rate=100.00%, first_token=0.000s, tokens/sec=0.00
2025-06-22 17:39:49,653 - __main__ - INFO - 
2025-06-22 17:39:49,653 - __main__ - INFO - FAIL: No Buffering Delays
2025-06-22 17:39:49,653 - __main__ - INFO -       Duration: 6.26s
2025-06-22 17:39:49,653 - __main__ - INFO -       Error: No token intervals measured
2025-06-22 17:39:49,653 - __main__ - INFO - 
2025-06-22 17:39:49,654 - __main__ - INFO - FAIL: Moderate Concurrent Load (100 requests)
2025-06-22 17:39:49,654 - __main__ - INFO -       Duration: 8.80s
2025-06-22 17:39:49,654 - __main__ - INFO -       Error: Success rate: 0.00%, Avg response time: 5.77s
2025-06-22 17:39:49,654 - __main__ - INFO - 
2025-06-22 17:39:49,654 - __main__ - INFO - FAIL: High Concurrent Load (500 requests)
2025-06-22 17:39:49,654 - __main__ - INFO -       Duration: 41.77s
2025-06-22 17:39:49,654 - __main__ - INFO -       Error: Success rate: 0.00%, Avg response time: 21.18s
2025-06-22 17:39:49,655 - __main__ - INFO - 
2025-06-22 17:39:49,655 - __main__ - INFO - FAIL: Extreme Load (1000 requests)
2025-06-22 17:39:49,655 - __main__ - INFO -       Duration: 105.88s
2025-06-22 17:39:49,655 - __main__ - INFO -       Error: Success rate too low: 0.00% (expected >70%)
2025-06-22 17:39:49,655 - __main__ - INFO - 
2025-06-22 17:39:49,655 - __main__ - INFO - 
PERFORMANCE METRICS:
2025-06-22 17:39:49,656 - __main__ - INFO - ------------------------------------------------------------
2025-06-22 17:39:49,656 - __main__ - INFO - 
================================================================================
2025-06-22 17:39:49,658 - __main__ - INFO - Detailed results saved to: tests/detailed_test_results.json
2025-06-22 17:39:49,659 - __main__ - ERROR - SIGNIFICANT TEST FAILURES (<80% success rate)
