# tests/test_concurrent_operations.py
"""
Test concurrent operations for SecuraMind API

Tests:
1. Simultaneous inference + ingestion operations
2. Multiple inference requests with unconsumed streams
3. Mixed workload handling (inference + ingestion)
"""

import asyncio
import time
import logging
from typing import List, Dict, Any, Tuple
from .test_config import APIClient, TestResult, TestLogger, TEST_CONFIG, TEST_DOCUMENTS

logger = logging.getLogger(__name__)

class ConcurrentOperationsTests:
    """Test suite for concurrent operations"""
    
    def __init__(self):
        self.test_logger = TestLogger("tests/concurrent_operations.log")
        self.results: List[TestResult] = []
        
    async def test_simultaneous_inference_and_ingestion(self) -> TestResult:
        """Test that inference can run while ingestion is happening"""
        test_name = "Simultaneous Inference and Ingestion"
        self.test_logger.log_test_start(test_name)
        start_time = time.time()
        
        try:
            async with APIClient() as client:
                # Setup initial collection with some data
                collection_name = f"test_simul_{int(time.time())}"
                status, data = await client.ingest_text(
                    collection_name,
                    "Initial test data for simultaneous operations",
                    "simul_test",
                    "simul_1"
                )
                
                if status != 200:
                    raise Exception(f"Failed to ingest initial data: {data}")
                
                # Wait for initial ingestion
                await asyncio.sleep(3)
                
                # Start a long-running ingestion operation
                ingestion_task = client.ingest_text(
                    collection_name,
                    "Large document content " * 100,  # Make it larger
                    "simul_large",
                    "simul_2"
                )
                
                # Immediately start inference requests while ingestion is running
                inference_tasks = []
                for i in range(3):
                    task = client.inference_request(
                        collection_name,
                        f"Query during ingestion {i}"
                    )
                    inference_tasks.append(task)
                
                # Execute ingestion and inference concurrently
                all_tasks = [ingestion_task] + inference_tasks
                results = await asyncio.gather(*all_tasks, return_exceptions=True)
                
                # Check ingestion result
                ingestion_result = results[0]
                if isinstance(ingestion_result, Exception):
                    raise Exception(f"Ingestion failed: {ingestion_result}")
                
                ing_status, ing_data = ingestion_result
                if ing_status != 200:
                    raise Exception(f"Ingestion failed with status {ing_status}: {ing_data}")
                
                # Check inference results
                inference_success_count = 0
                for i, result in enumerate(results[1:], 1):
                    if isinstance(result, Exception):
                        logger.error(f"Inference {i} failed: {result}")
                    else:
                        status_code, tokens = result
                        if status_code == 200 and len(tokens) > 0:
                            inference_success_count += 1
                            logger.info(f"Inference {i} succeeded with {len(tokens)} tokens")
                
                # All operations should succeed
                if inference_success_count == 3:
                    result = TestResult(test_name, True, time.time() - start_time)
                else:
                    result = TestResult(
                        test_name,
                        False,
                        time.time() - start_time,
                        f"Only {inference_success_count}/3 inference requests succeeded"
                    )
                    
        except Exception as e:
            result = TestResult(test_name, False, time.time() - start_time, str(e))
            
        self.test_logger.log_test_result(result)
        self.test_logger.log_test_end(test_name)
        return result
        
    async def test_multiple_inference_unconsumed_streams(self) -> TestResult:
        """Test multiple inference requests where streams are not fully consumed"""
        test_name = "Multiple Inference with Unconsumed Streams"
        self.test_logger.log_test_start(test_name)
        start_time = time.time()
        
        try:
            async with APIClient() as client:
                # Setup test collection
                collection_name = f"test_unconsumed_{int(time.time())}"
                status, data = await client.ingest_text(
                    collection_name,
                    "Test data for unconsumed stream testing",
                    "unconsumed_test",
                    "unconsumed_1"
                )
                
                if status != 200:
                    raise Exception(f"Failed to ingest test data: {data}")
                
                await asyncio.sleep(2)
                
                # Create a custom client method that doesn't consume full streams
                async def partial_inference_request(collection_name: str, query: str) -> Tuple[int, int]:
                    """Make inference request but only read first few tokens"""
                    payload = {
                        "collection_name": collection_name,
                        "query": query,
                        "language": "en"
                    }
                    
                    token_count = 0
                    status_code = 500
                    
                    try:
                        async with client.session.post(
                            f"{client.base_url}/api/v1/inference",
                            json=payload
                        ) as response:
                            status_code = response.status
                            
                            if response.status == 200:
                                # Only read first 3 tokens, then abandon stream
                                async for line in response.content:
                                    line = line.decode('utf-8').strip()
                                    if line.startswith('data: '):
                                        try:
                                            import json
                                            data = json.loads(line[6:])
                                            if 'token' in data:
                                                token_count += 1
                                                if token_count >= 3:
                                                    break  # Abandon stream early
                                        except json.JSONDecodeError:
                                            continue
                                            
                    except Exception as e:
                        logger.error(f"Partial request error: {e}")
                        return 500, 0
                        
                    return status_code, token_count
                
                # Send multiple requests with unconsumed streams
                num_requests = 4
                tasks = []
                
                for i in range(num_requests):
                    task = partial_inference_request(collection_name, f"Unconsumed query {i}")
                    tasks.append(task)
                
                # Execute concurrently
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Verify all requests started successfully
                success_count = 0
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        logger.error(f"Request {i} failed: {result}")
                    else:
                        status_code, token_count = result
                        if status_code == 200 and token_count > 0:
                            success_count += 1
                            logger.info(f"Request {i} started successfully, got {token_count} tokens")
                
                # Wait a bit to see if server remains stable
                await asyncio.sleep(5)
                
                # Test that server is still responsive after unconsumed streams
                health_ok, health_data = await client.health_check()
                if not health_ok:
                    raise Exception("Server became unresponsive after unconsumed streams")
                
                if success_count == num_requests:
                    result = TestResult(test_name, True, time.time() - start_time)
                else:
                    result = TestResult(
                        test_name,
                        False,
                        time.time() - start_time,
                        f"Only {success_count}/{num_requests} requests succeeded"
                    )
                    
        except Exception as e:
            result = TestResult(test_name, False, time.time() - start_time, str(e))
            
        self.test_logger.log_test_result(result)
        self.test_logger.log_test_end(test_name)
        return result
        
    async def test_mixed_workload_handling(self) -> TestResult:
        """Test handling of mixed inference and ingestion workloads"""
        test_name = "Mixed Workload Handling"
        self.test_logger.log_test_start(test_name)
        start_time = time.time()
        
        try:
            async with APIClient() as client:
                # Setup base collection
                collection_name = f"test_mixed_{int(time.time())}"
                status, data = await client.ingest_text(
                    collection_name,
                    "Base data for mixed workload testing",
                    "mixed_base",
                    "mixed_1"
                )
                
                if status != 200:
                    raise Exception(f"Failed to ingest base data: {data}")
                
                await asyncio.sleep(2)
                
                # Create mixed workload: alternating inference and ingestion
                tasks = []
                
                # Add inference tasks
                for i in range(3):
                    task = client.inference_request(collection_name, f"Mixed query {i}")
                    tasks.append(("inference", task))
                
                # Add ingestion tasks
                for i, doc in enumerate(TEST_DOCUMENTS[:2]):
                    task = client.ingest_text(
                        collection_name,
                        doc["text"],
                        f"mixed_{doc['source']}",
                        f"mixed_{doc['identifier']}_{i}"
                    )
                    tasks.append(("ingestion", task))
                
                # Shuffle tasks to create mixed workload
                import random
                random.shuffle(tasks)
                
                # Execute all tasks concurrently
                task_futures = [task for _, task in tasks]
                results = await asyncio.gather(*task_futures, return_exceptions=True)
                
                # Analyze results
                inference_success = 0
                ingestion_success = 0
                
                for i, (task_type, result) in enumerate(zip([t[0] for t in tasks], results)):
                    if isinstance(result, Exception):
                        logger.error(f"{task_type.title()} task {i} failed: {result}")
                        continue
                    
                    if task_type == "inference":
                        status_code, tokens = result
                        if status_code == 200 and len(tokens) > 0:
                            inference_success += 1
                            logger.info(f"Inference task {i} succeeded with {len(tokens)} tokens")
                    else:  # ingestion
                        status_code, data = result
                        if status_code == 200:
                            ingestion_success += 1
                            logger.info(f"Ingestion task {i} succeeded")
                
                # Verify all tasks succeeded
                expected_inference = 3
                expected_ingestion = 2
                
                if inference_success == expected_inference and ingestion_success == expected_ingestion:
                    result = TestResult(test_name, True, time.time() - start_time)
                else:
                    result = TestResult(
                        test_name,
                        False,
                        time.time() - start_time,
                        f"Inference: {inference_success}/{expected_inference}, Ingestion: {ingestion_success}/{expected_ingestion}"
                    )
                    
        except Exception as e:
            result = TestResult(test_name, False, time.time() - start_time, str(e))
            
        self.test_logger.log_test_result(result)
        self.test_logger.log_test_end(test_name)
        return result
        
    async def run_all_tests(self) -> List[TestResult]:
        """Run all concurrent operations tests"""
        logger.info("Starting Concurrent Operations Tests")
        
        tests = [
            self.test_simultaneous_inference_and_ingestion,
            self.test_multiple_inference_unconsumed_streams,
            self.test_mixed_workload_handling
        ]
        
        for test_func in tests:
            result = await test_func()
            self.results.append(result)
            
            # Brief pause between tests
            await asyncio.sleep(2)
            
        return self.results

# Main execution for standalone testing
async def main():
    """Run concurrent operations tests standalone"""
    test_suite = ConcurrentOperationsTests()
    results = await test_suite.run_all_tests()
    
    # Summary
    passed = sum(1 for r in results if r.success)
    total = len(results)
    
    print(f"\n=== CONCURRENT OPERATIONS TESTS SUMMARY ===")
    print(f"Passed: {passed}/{total}")
    print(f"Failed: {total - passed}/{total}")
    
    for result in results:
        status = "PASS" if result.success else "FAIL"
        print(f"  {status}: {result.test_name} ({result.duration:.2f}s)")
        if not result.success:
            print(f"    Error: {result.error_message}")

if __name__ == "__main__":
    asyncio.run(main())
