# tests/test_thread_safety.py
"""
Test thread safety for SecuraMind API

Tests:
1. Verify LLM instances are never accessed by multiple requests simultaneously
2. Test locking mechanism prevents concurrent access to same LLM instance
3. Validate GGML_ASSERT failure prevention
"""

import asyncio
import time
import logging
import threading
from typing import List, Dict, Any, Tuple
from .test_config import APIClient, TestResult, TestLogger, TEST_CONFIG

logger = logging.getLogger(__name__)

class ThreadSafetyTests:
    """Test suite for thread safety verification"""
    
    def __init__(self):
        self.test_logger = TestLogger("tests/thread_safety.log")
        self.results: List[TestResult] = []
        
    async def test_llm_instance_locking(self) -> TestResult:
        """Test that LLM instances are properly locked during use"""
        test_name = "LLM Instance Locking"
        self.test_logger.log_test_start(test_name)
        start_time = time.time()
        
        try:
            async with APIClient() as client:
                # Setup test collection
                collection_name = f"test_locking_{int(time.time())}"
                status, data = await client.ingest_text(
                    collection_name,
                    "Test data for thread safety locking verification",
                    "locking_test",
                    "lock_1"
                )
                
                if status != 200:
                    raise Exception(f"Failed to ingest test data: {data}")
                
                await asyncio.sleep(3)
                
                # Send multiple concurrent requests to test locking
                # With 2 LLMs (main + backup), the 3rd request should queue
                num_requests = 6
                tasks = []
                request_start_times = []
                
                logger.info(f"Starting {num_requests} concurrent requests to test locking")
                
                for i in range(num_requests):
                    request_start_times.append(time.time())
                    task = client.inference_request(collection_name, f"Locking test query {i}")
                    tasks.append(task)
                
                # Execute all requests concurrently
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Analyze results for thread safety indicators
                success_count = 0
                ggml_errors = 0
                concurrent_access_errors = 0
                
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        error_str = str(result).lower()
                        if "ggml_assert" in error_str or "tensor buffer not set" in error_str:
                            ggml_errors += 1
                            logger.error(f"Request {i} failed with GGML_ASSERT: {result}")
                        elif "concurrent" in error_str or "lock" in error_str:
                            concurrent_access_errors += 1
                            logger.error(f"Request {i} failed with concurrent access error: {result}")
                        else:
                            logger.error(f"Request {i} failed with other error: {result}")
                    else:
                        status_code, tokens = result
                        if status_code == 200 and len(tokens) > 0:
                            success_count += 1
                            response_time = time.time() - request_start_times[i]
                            logger.info(f"Request {i} succeeded in {response_time:.2f}s with {len(tokens)} tokens")
                        else:
                            logger.error(f"Request {i} failed with status {status_code}")
                
                # Success criteria: No GGML_ASSERT errors (thread safety working)
                if ggml_errors == 0:
                    result = TestResult(
                        test_name,
                        True,
                        time.time() - start_time,
                        response_data={
                            "success_count": success_count,
                            "total_requests": num_requests,
                            "ggml_errors": ggml_errors,
                            "concurrent_access_errors": concurrent_access_errors
                        }
                    )
                else:
                    result = TestResult(
                        test_name,
                        False,
                        time.time() - start_time,
                        f"GGML_ASSERT errors detected: {ggml_errors}. Thread safety failed!"
                    )
                    
        except Exception as e:
            result = TestResult(test_name, False, time.time() - start_time, str(e))
            
        self.test_logger.log_test_result(result)
        self.test_logger.log_test_end(test_name)
        return result
        
    async def test_concurrent_access_prevention(self) -> TestResult:
        """Test that concurrent access to same LLM instance is prevented"""
        test_name = "Concurrent Access Prevention"
        self.test_logger.log_test_start(test_name)
        start_time = time.time()
        
        try:
            async with APIClient() as client:
                # Setup test collection
                collection_name = f"test_prevention_{int(time.time())}"
                status, data = await client.ingest_text(
                    collection_name,
                    "Test data for concurrent access prevention",
                    "prevention_test",
                    "prevent_1"
                )
                
                if status != 200:
                    raise Exception(f"Failed to ingest test data: {data}")
                
                await asyncio.sleep(3)
                
                # Test with rapid-fire requests to stress the locking mechanism
                num_waves = 3
                requests_per_wave = 8
                all_results = []
                
                for wave in range(num_waves):
                    logger.info(f"Starting wave {wave + 1} with {requests_per_wave} rapid requests")
                    
                    # Send requests in quick succession
                    tasks = []
                    for i in range(requests_per_wave):
                        task = client.inference_request(
                            collection_name, 
                            f"Wave {wave} rapid query {i}"
                        )
                        tasks.append(task)
                        # Very small delay to create rapid succession
                        await asyncio.sleep(0.01)
                    
                    # Execute wave concurrently
                    wave_results = await asyncio.gather(*tasks, return_exceptions=True)
                    all_results.extend(wave_results)
                    
                    # Brief pause between waves
                    await asyncio.sleep(2)
                
                # Analyze all results for thread safety violations
                total_requests = num_waves * requests_per_wave
                success_count = 0
                thread_safety_violations = 0
                
                for i, result in enumerate(all_results):
                    if isinstance(result, Exception):
                        error_str = str(result).lower()
                        if any(keyword in error_str for keyword in [
                            "ggml_assert", "tensor buffer", "concurrent", 
                            "thread", "lock", "race condition"
                        ]):
                            thread_safety_violations += 1
                            logger.error(f"Thread safety violation in request {i}: {result}")
                        else:
                            logger.error(f"Other error in request {i}: {result}")
                    else:
                        status_code, tokens = result
                        if status_code == 200 and len(tokens) > 0:
                            success_count += 1
                
                logger.info(f"Concurrent access test results: {success_count}/{total_requests} successful")
                logger.info(f"Thread safety violations: {thread_safety_violations}")
                
                # Success criteria: No thread safety violations
                if thread_safety_violations == 0:
                    result = TestResult(
                        test_name,
                        True,
                        time.time() - start_time,
                        response_data={
                            "total_requests": total_requests,
                            "successful_requests": success_count,
                            "thread_safety_violations": thread_safety_violations
                        }
                    )
                else:
                    result = TestResult(
                        test_name,
                        False,
                        time.time() - start_time,
                        f"Thread safety violations detected: {thread_safety_violations}"
                    )
                    
        except Exception as e:
            result = TestResult(test_name, False, time.time() - start_time, str(e))
            
        self.test_logger.log_test_result(result)
        self.test_logger.log_test_end(test_name)
        return result
        
    async def test_ggml_assert_prevention(self) -> TestResult:
        """Test that GGML_ASSERT failures are prevented by thread safety measures"""
        test_name = "GGML_ASSERT Prevention"
        self.test_logger.log_test_start(test_name)
        start_time = time.time()
        
        try:
            async with APIClient() as client:
                # Setup test collection
                collection_name = f"test_ggml_{int(time.time())}"
                status, data = await client.ingest_text(
                    collection_name,
                    "Test data for GGML_ASSERT prevention verification",
                    "ggml_test",
                    "ggml_1"
                )
                
                if status != 200:
                    raise Exception(f"Failed to ingest test data: {data}")
                
                await asyncio.sleep(3)
                
                # Create conditions that previously caused GGML_ASSERT failures
                # High concurrency with varying request patterns
                test_patterns = [
                    # Pattern 1: Burst of concurrent requests
                    {"name": "burst", "requests": 10, "delay": 0},
                    # Pattern 2: Overlapping requests with delays
                    {"name": "overlapping", "requests": 6, "delay": 0.1},
                    # Pattern 3: Rapid sequential requests
                    {"name": "rapid_sequential", "requests": 8, "delay": 0.05}
                ]
                
                all_ggml_errors = 0
                all_successful = 0
                pattern_results = {}
                
                for pattern in test_patterns:
                    logger.info(f"Testing pattern: {pattern['name']}")
                    
                    tasks = []
                    for i in range(pattern["requests"]):
                        task = client.inference_request(
                            collection_name,
                            f"GGML test {pattern['name']} query {i}"
                        )
                        tasks.append(task)
                        
                        if pattern["delay"] > 0:
                            await asyncio.sleep(pattern["delay"])
                    
                    # Execute pattern
                    pattern_start = time.time()
                    results = await asyncio.gather(*tasks, return_exceptions=True)
                    pattern_duration = time.time() - pattern_start
                    
                    # Analyze pattern results
                    pattern_success = 0
                    pattern_ggml_errors = 0
                    
                    for i, result in enumerate(results):
                        if isinstance(result, Exception):
                            error_str = str(result).lower()
                            if "ggml_assert" in error_str or "tensor buffer not set" in error_str:
                                pattern_ggml_errors += 1
                                all_ggml_errors += 1
                                logger.error(f"GGML_ASSERT in {pattern['name']} request {i}: {result}")
                        else:
                            status_code, tokens = result
                            if status_code == 200 and len(tokens) > 0:
                                pattern_success += 1
                                all_successful += 1
                    
                    pattern_results[pattern["name"]] = {
                        "success": pattern_success,
                        "ggml_errors": pattern_ggml_errors,
                        "duration": pattern_duration
                    }
                    
                    logger.info(f"Pattern {pattern['name']}: {pattern_success}/{pattern['requests']} successful, {pattern_ggml_errors} GGML errors")
                    
                    # Brief pause between patterns
                    await asyncio.sleep(1)
                
                # Final verification: Check server health after stress testing
                health_ok, health_data = await client.health_check()
                if not health_ok:
                    raise Exception("Server health check failed after GGML stress test")
                
                # Success criteria: Zero GGML_ASSERT errors across all patterns
                if all_ggml_errors == 0:
                    result = TestResult(
                        test_name,
                        True,
                        time.time() - start_time,
                        response_data={
                            "total_successful": all_successful,
                            "total_ggml_errors": all_ggml_errors,
                            "pattern_results": pattern_results,
                            "server_healthy": health_ok
                        }
                    )
                else:
                    result = TestResult(
                        test_name,
                        False,
                        time.time() - start_time,
                        f"GGML_ASSERT failures detected: {all_ggml_errors} total across all patterns"
                    )
                    
        except Exception as e:
            result = TestResult(test_name, False, time.time() - start_time, str(e))
            
        self.test_logger.log_test_result(result)
        self.test_logger.log_test_end(test_name)
        return result
        
    async def run_all_tests(self) -> List[TestResult]:
        """Run all thread safety tests"""
        logger.info("Starting Thread Safety Tests")
        
        tests = [
            self.test_llm_instance_locking,
            self.test_concurrent_access_prevention,
            self.test_ggml_assert_prevention
        ]
        
        for test_func in tests:
            result = await test_func()
            self.results.append(result)
            
            # Pause between tests to let system stabilize
            await asyncio.sleep(5)
            
        return self.results

# Main execution for standalone testing
async def main():
    """Run thread safety tests standalone"""
    test_suite = ThreadSafetyTests()
    results = await test_suite.run_all_tests()
    
    # Summary
    passed = sum(1 for r in results if r.success)
    total = len(results)
    
    print(f"\n=== THREAD SAFETY TESTS SUMMARY ===")
    print(f"Passed: {passed}/{total}")
    print(f"Failed: {total - passed}/{total}")
    
    for result in results:
        status = "PASS" if result.success else "FAIL"
        print(f"  {status}: {result.test_name} ({result.duration:.2f}s)")
        if result.response_data and "total_ggml_errors" in result.response_data:
            print(f"    GGML Errors: {result.response_data['total_ggml_errors']}")
        if not result.success:
            print(f"    Error: {result.error_message}")

if __name__ == "__main__":
    asyncio.run(main())
