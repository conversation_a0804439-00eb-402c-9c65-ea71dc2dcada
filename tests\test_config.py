# tests/test_config.py
"""
Test configuration and utilities for SecuraMind test suite
"""

import os
import time
import json
import logging
import asyncio
import aiohttp
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

# Test Configuration
TEST_CONFIG = {
    "base_url": "http://127.0.0.1:8000",
    "timeout": 30,
    "max_retries": 3,
    "test_collection": "test_collection",
    "stress_test_requests": 1000,
    "concurrent_test_requests": 100,
    "request_timeout": 60,
    "stream_timeout": 120,
    "health_check_interval": 5,
    "log_level": "INFO"
}

# Test data
TEST_DOCUMENTS = [
    {
        "text": "The secret codename is: apple. This is a test document for SecuraMind.",
        "source": "test_doc_1",
        "identifier": "test_1"
    },
    {
        "text": "Machine learning is a subset of artificial intelligence that focuses on algorithms.",
        "source": "test_doc_2", 
        "identifier": "test_2"
    },
    {
        "text": "SecuraMind provides secure AI inference with local LLM processing.",
        "source": "test_doc_3",
        "identifier": "test_3"
    }
]

TEST_QUERIES = [
    "What is the secret codename?",
    "Tell me about machine learning",
    "What is SecuraMind?",
    "Explain artificial intelligence",
    "What are the main features?"
]

@dataclass
class TestResult:
    """Container for test results"""
    test_name: str
    success: bool
    duration: float
    error_message: Optional[str] = None
    response_data: Optional[Dict] = None
    status_code: Optional[int] = None
    
class TestLogger:
    """Centralized logging for tests"""
    
    def __init__(self, log_file: str = "tests/test_results.log"):
        self.log_file = log_file
        self.setup_logging()
        
    def setup_logging(self):
        """Setup logging configuration"""
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)
        
        logging.basicConfig(
            level=getattr(logging, TEST_CONFIG["log_level"]),
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            handlers=[
                logging.FileHandler(self.log_file),
                logging.StreamHandler()
            ]
        )
        
    def log_test_start(self, test_name: str):
        """Log test start"""
        logging.info(f"=== STARTING TEST: {test_name} ===")
        
    def log_test_result(self, result: TestResult):
        """Log test result"""
        status = "PASSED" if result.success else "FAILED"
        logging.info(f"=== TEST {status}: {result.test_name} (Duration: {result.duration:.2f}s) ===")
        if not result.success and result.error_message:
            logging.error(f"Error: {result.error_message}")
            
    def log_test_end(self, test_name: str):
        """Log test end"""
        logging.info(f"=== COMPLETED TEST: {test_name} ===\n")

class APIClient:
    """HTTP client for API testing"""
    
    def __init__(self, base_url: str = TEST_CONFIG["base_url"]):
        self.base_url = base_url
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=TEST_CONFIG["timeout"])
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
            
    async def health_check(self) -> Tuple[bool, Dict]:
        """Check API health"""
        try:
            async with self.session.get(f"{self.base_url}/api/v1/health") as response:
                data = await response.json()
                return response.status == 200, data
        except Exception as e:
            return False, {"error": str(e)}
            
    async def ingest_text(self, collection_name: str, text: str, source: str, identifier: str) -> Tuple[int, Dict]:
        """Ingest text data"""
        payload = {
            "collection_name": collection_name,
            "text_content": text,
            "source_name": source,
            "source_identifier": identifier
        }
        
        try:
            async with self.session.post(
                f"{self.base_url}/api/v1/ingest/text",
                json=payload
            ) as response:
                data = await response.json()
                return response.status, data
        except Exception as e:
            return 500, {"error": str(e)}
            
    async def inference_request(self, collection_name: str, query: str, language: str = "en") -> Tuple[int, List[str]]:
        """Make inference request and collect streaming tokens"""
        payload = {
            "collection_name": collection_name,
            "query": query,
            "language": language
        }
        
        tokens = []
        status_code = 500
        
        try:
            async with self.session.post(
                f"{self.base_url}/api/v1/inference",
                json=payload
            ) as response:
                status_code = response.status
                
                if response.status == 200:
                    async for line in response.content:
                        line = line.decode('utf-8').strip()
                        if line.startswith('data: '):
                            try:
                                data = json.loads(line[6:])
                                if 'token' in data:
                                    tokens.append(data['token'])
                                elif 'event' in data and data['event'] == 'eos':
                                    break
                            except json.JSONDecodeError:
                                continue
                                
        except Exception as e:
            return 500, [f"Error: {str(e)}"]
            
        return status_code, tokens

def wait_for_server_ready(max_wait: int = 60) -> bool:
    """Wait for server to be ready"""
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        try:
            import requests
            response = requests.get(f"{TEST_CONFIG['base_url']}/api/v1/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if (data.get("main_llm_available", False) or 
                    data.get("backup_llm_available", False)):
                    return True
        except:
            pass
            
        time.sleep(2)
        
    return False

def cleanup_test_data():
    """Clean up test data"""
    # This would typically clean up test collections, but we'll keep it simple
    # for now since Qdrant cleanup is complex
    pass
