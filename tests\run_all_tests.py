#!/usr/bin/env python3
# tests/run_all_tests.py
"""
Master Test Runner for SecuraMind API

Executes all test suites systematically and generates consolidated logs.
Designed to run all tests on a single server instance without restarts.
"""

import asyncio
import time
import logging
import json
import sys
import os
from datetime import datetime
from typing import List, Dict, Any
from pathlib import Path

# Add the project root to the path so we can import test modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from tests.test_config import TestLogger, wait_for_server_ready, TEST_CONFIG
from tests.test_concurrent_requests import ConcurrentRequestTests
from tests.test_concurrent_operations import ConcurrentOperationsTests
from tests.test_scalability_stress import ScalabilityStressTests
from tests.test_data_isolation import DataIsolationTests
from tests.test_thread_safety import ThreadSafetyTests
from tests.test_error_recovery import ErrorRecoveryTests
from tests.test_streaming import StreamingTests

logger = logging.getLogger(__name__)

class MasterTestRunner:
    """Master test runner that executes all test suites systematically"""
    
    def __init__(self):
        self.start_time = time.time()
        self.test_logger = TestLogger("tests/master_test_results.log")
        self.all_results = []
        self.test_suites = []
        
    def setup_test_suites(self):
        """Initialize all test suites"""
        self.test_suites = [
            ("Concurrent Request Handling", ConcurrentRequestTests()),
            ("Concurrent Operations", ConcurrentOperationsTests()),
            ("Data Isolation and Security", DataIsolationTests()),
            ("Thread Safety", ThreadSafetyTests()),
            ("Error Recovery and Resilience", ErrorRecoveryTests()),
            ("Streaming Response", StreamingTests()),
            ("Scalability and Stress", ScalabilityStressTests())  # Run stress tests last
        ]
        
    async def check_server_health(self) -> bool:
        """Check if server is healthy before running tests"""
        logger.info("Checking server health before starting tests...")
        
        from tests.test_config import APIClient
        
        try:
            async with APIClient() as client:
                health_ok, health_data = await client.health_check()
                
                if health_ok:
                    logger.info("Server health check passed:")
                    logger.info(f"  Main LLM: {health_data.get('main_llm_status')} (Available: {health_data.get('main_llm_available')})")
                    logger.info(f"  Backup LLM: {health_data.get('backup_llm_status')} (Available: {health_data.get('backup_llm_available')})")
                    logger.info(f"  Concurrent capacity: {health_data.get('concurrent_capacity', 0)}")
                    return True
                else:
                    logger.error(f"Server health check failed: {health_data}")
                    return False
                    
        except Exception as e:
            logger.error(f"Failed to check server health: {e}")
            return False
            
    async def run_test_suite(self, suite_name: str, test_suite) -> Dict[str, Any]:
        """Run a single test suite and return results"""
        logger.info(f"\n{'='*60}")
        logger.info(f"STARTING TEST SUITE: {suite_name}")
        logger.info(f"{'='*60}")
        
        suite_start_time = time.time()
        
        try:
            # Run the test suite
            results = await test_suite.run_all_tests()
            
            # Calculate suite statistics
            passed = sum(1 for r in results if r.success)
            failed = len(results) - passed
            suite_duration = time.time() - suite_start_time
            
            suite_summary = {
                "suite_name": suite_name,
                "total_tests": len(results),
                "passed": passed,
                "failed": failed,
                "success_rate": passed / len(results) if results else 0,
                "duration": suite_duration,
                "results": results
            }
            
            logger.info(f"\nTEST SUITE COMPLETED: {suite_name}")
            logger.info(f"  Tests: {passed}/{len(results)} passed ({suite_summary['success_rate']:.1%})")
            logger.info(f"  Duration: {suite_duration:.2f}s")
            
            if failed > 0:
                logger.warning(f"  FAILURES in {suite_name}:")
                for result in results:
                    if not result.success:
                        logger.warning(f"    - {result.test_name}: {result.error_message}")
            
            return suite_summary
            
        except Exception as e:
            logger.error(f"TEST SUITE FAILED: {suite_name} - {e}")
            return {
                "suite_name": suite_name,
                "total_tests": 0,
                "passed": 0,
                "failed": 1,
                "success_rate": 0,
                "duration": time.time() - suite_start_time,
                "error": str(e),
                "results": []
            }
            
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all test suites systematically"""
        logger.info("="*80)
        logger.info("SECURAMIND API COMPREHENSIVE TEST SUITE")
        logger.info("="*80)
        logger.info(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"Test configuration: {TEST_CONFIG}")
        
        # Check server health first
        if not await self.check_server_health():
            logger.error("Server health check failed. Cannot proceed with tests.")
            return {
                "success": False,
                "error": "Server health check failed",
                "suites": [],
                "total_duration": time.time() - self.start_time
            }
        
        # Setup test suites
        self.setup_test_suites()
        
        suite_results = []
        total_tests = 0
        total_passed = 0
        total_failed = 0
        
        # Run each test suite
        for suite_name, test_suite in self.test_suites:
            # Check server health between suites
            logger.info(f"\nChecking server health before {suite_name}...")
            if not await self.check_server_health():
                logger.warning(f"Server health degraded before {suite_name}. Continuing anyway...")
            
            # Run the suite
            suite_result = await self.run_test_suite(suite_name, test_suite)
            suite_results.append(suite_result)
            
            # Update totals
            total_tests += suite_result["total_tests"]
            total_passed += suite_result["passed"]
            total_failed += suite_result["failed"]
            
            # Collect individual test results
            self.all_results.extend(suite_result.get("results", []))
            
            # Pause between suites to let system stabilize
            logger.info(f"Pausing 10 seconds before next test suite...")
            await asyncio.sleep(10)
        
        # Final server health check
        logger.info("\nFinal server health check...")
        final_health_ok = await self.check_server_health()
        
        # Calculate overall statistics
        total_duration = time.time() - self.start_time
        overall_success_rate = total_passed / total_tests if total_tests > 0 else 0
        
        master_results = {
            "success": True,
            "start_time": datetime.fromtimestamp(self.start_time).isoformat(),
            "end_time": datetime.now().isoformat(),
            "total_duration": total_duration,
            "total_tests": total_tests,
            "total_passed": total_passed,
            "total_failed": total_failed,
            "overall_success_rate": overall_success_rate,
            "final_server_health": final_health_ok,
            "suites": suite_results,
            "test_config": TEST_CONFIG
        }
        
        return master_results
        
    def generate_summary_report(self, results: Dict[str, Any]):
        """Generate a comprehensive summary report"""
        logger.info("\n" + "="*80)
        logger.info("COMPREHENSIVE TEST RESULTS SUMMARY")
        logger.info("="*80)
        
        logger.info(f"Test execution completed: {results['end_time']}")
        logger.info(f"Total duration: {results['total_duration']:.2f} seconds")
        logger.info(f"Total tests executed: {results['total_tests']}")
        logger.info(f"Overall success rate: {results['overall_success_rate']:.1%}")
        logger.info(f"Final server health: {'HEALTHY' if results['final_server_health'] else 'DEGRADED'}")
        
        logger.info(f"\nRESULTS BY TEST SUITE:")
        logger.info("-" * 60)
        
        for suite in results['suites']:
            status = "PASS" if suite['success_rate'] == 1.0 else "FAIL" if suite['success_rate'] == 0 else "PARTIAL"
            logger.info(f"{suite['suite_name']:30} | {suite['passed']:2}/{suite['total_tests']:2} | {suite['success_rate']:6.1%} | {status}")
        
        # Detailed failure analysis
        failed_tests = [r for r in self.all_results if not r.success]
        if failed_tests:
            logger.info(f"\nFAILED TESTS ANALYSIS ({len(failed_tests)} failures):")
            logger.info("-" * 60)
            
            for test in failed_tests:
                logger.info(f"FAIL: {test.test_name}")
                logger.info(f"      Duration: {test.duration:.2f}s")
                logger.info(f"      Error: {test.error_message}")
                logger.info("")
        
        # Performance metrics
        logger.info(f"\nPERFORMANCE METRICS:")
        logger.info("-" * 60)
        
        # Find performance-related test results
        perf_tests = [r for r in self.all_results if r.response_data and 
                     any(key in r.response_data for key in ['success_rate', 'avg_response_time', 'tokens_per_second'])]
        
        for test in perf_tests:
            if test.response_data:
                logger.info(f"{test.test_name}:")
                data = test.response_data
                if 'success_rate' in data:
                    logger.info(f"  Success Rate: {data['success_rate']:.1%}")
                if 'avg_response_time' in data:
                    logger.info(f"  Avg Response Time: {data['avg_response_time']:.2f}s")
                if 'tokens_per_second' in data:
                    logger.info(f"  Tokens/Second: {data['tokens_per_second']:.2f}")
                if 'total_requests' in data:
                    logger.info(f"  Total Requests: {data['total_requests']}")
        
        logger.info("\n" + "="*80)
        
    def save_detailed_results(self, results: Dict[str, Any]):
        """Save detailed results to JSON file"""
        results_file = "tests/detailed_test_results.json"
        
        # Convert TestResult objects to dictionaries for JSON serialization
        json_results = results.copy()
        for suite in json_results['suites']:
            suite_results = []
            for result in suite.get('results', []):
                suite_results.append({
                    "test_name": result.test_name,
                    "success": result.success,
                    "duration": result.duration,
                    "error_message": result.error_message,
                    "response_data": result.response_data,
                    "status_code": result.status_code
                })
            suite['results'] = suite_results
        
        try:
            with open(results_file, 'w') as f:
                json.dump(json_results, f, indent=2)
            logger.info(f"Detailed results saved to: {results_file}")
        except Exception as e:
            logger.error(f"Failed to save detailed results: {e}")

async def main():
    """Main entry point for the master test runner"""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler("tests/master_test_execution.log"),
            logging.StreamHandler()
        ]
    )
    
    # Wait for server to be ready
    logger.info("Waiting for SecuraMind server to be ready...")
    if not wait_for_server_ready(max_wait=120):
        logger.error("Server is not ready. Please start the SecuraMind server first.")
        sys.exit(1)
    
    logger.info("Server is ready. Starting comprehensive test suite...")
    
    # Run all tests
    runner = MasterTestRunner()
    results = await runner.run_all_tests()
    
    # Generate reports
    runner.generate_summary_report(results)
    runner.save_detailed_results(results)
    
    # Exit with appropriate code
    if results['overall_success_rate'] == 1.0:
        logger.info("ALL TESTS PASSED! 🎉")
        sys.exit(0)
    elif results['overall_success_rate'] >= 0.8:
        logger.warning("MOST TESTS PASSED (>80% success rate)")
        sys.exit(0)
    else:
        logger.error("SIGNIFICANT TEST FAILURES (<80% success rate)")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
