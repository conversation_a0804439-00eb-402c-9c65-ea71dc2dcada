{"success": true, "start_time": "2025-06-22T17:31:34.534948", "end_time": "2025-06-22T17:39:49.643876", "total_duration": 495.*************, "total_tests": 22, "total_passed": 9, "total_failed": 13, "overall_success_rate": 0.****************, "final_server_health": true, "suites": [{"suite_name": "Concurrent Request Handling", "total_tests": 3, "passed": 3, "failed": 0, "success_rate": 1.0, "duration": 70.**************, "results": [{"test_name": "LLM Routing: Main to Backup", "success": true, "duration": 14.***************, "error_message": null, "response_data": null, "status_code": null}, {"test_name": "Request Queueing When LLMs Busy", "success": true, "duration": 29.***************, "error_message": null, "response_data": null, "status_code": null}, {"test_name": "Request Ordering", "success": true, "duration": 23.***************, "error_message": null, "response_data": null, "status_code": null}]}, {"suite_name": "Concurrent Operations", "total_tests": 3, "passed": 1, "failed": 2, "success_rate": 0.****************, "duration": 31.**************, "results": [{"test_name": "Simultaneous Inference and Ingestion", "success": true, "duration": 15.***************, "error_message": null, "response_data": null, "status_code": null}, {"test_name": "Multiple Inference with Unconsumed Streams", "success": false, "duration": 7.**************, "error_message": "Only 1/4 requests succeeded", "response_data": null, "status_code": null}, {"test_name": "Mixed Workload Handling", "success": false, "duration": 2.3853697776794434, "error_message": "Inference: 0/3, Ingestion: 2/2", "response_data": null, "status_code": null}]}, {"suite_name": "Data Isolation and Security", "total_tests": 3, "passed": 0, "failed": 3, "success_rate": 0.0, "duration": 31.057328701019287, "results": [{"test_name": "User Request Isolation", "success": false, "duration": 5.183796644210815, "error_message": "Isolation failures: User user_a got status 500; User user_b didn't get their secret 'banana'; User user_c didn't get their secret 'cherry'", "response_data": null, "status_code": null}, {"test_name": "Concurrent Session Isolation", "success": false, "duration": 5.3264830112457275, "error_message": "Session session_0 round 0 got status 500", "response_data": null, "status_code": null}, {"test_name": "Multi-User Concurrent Isolation", "success": false, "duration": 11.514933347702026, "error_message": "Isolation violations: User user_00 missing own sensitive info; User user_01 missing own sensitive info; User user_02 missing own sensitive info; User user_03 missing own sensitive info; User user_04 missing own sensitive info...", "response_data": null, "status_code": null}]}, {"suite_name": "Thread Safety", "total_tests": 3, "passed": 3, "failed": 0, "success_rate": 1.0, "duration": 38.27564811706543, "results": [{"test_name": "LLM Instance Locking", "success": true, "duration": 3.521282434463501, "error_message": null, "response_data": {"success_count": 0, "total_requests": 6, "ggml_errors": 0, "concurrent_access_errors": 0}, "status_code": null}, {"test_name": "Concurrent Access Prevention", "success": true, "duration": 10.793382406234741, "error_message": null, "response_data": {"total_requests": 24, "successful_requests": 0, "thread_safety_violations": 0}, "status_code": null}, {"test_name": "GGML_ASSERT Prevention", "success": true, "duration": 8.942202806472778, "error_message": null, "response_data": {"total_successful": 0, "total_ggml_errors": 0, "pattern_results": {"burst": {"success": 0, "ggml_errors": 0, "duration": 0.7125594615936279}, "overlapping": {"success": 0, "ggml_errors": 0, "duration": 0.38150811195373535}, "rapid_sequential": {"success": 0, "ggml_errors": 0, "duration": 0.662992000579834}}, "server_healthy": true}, "status_code": null}]}, {"suite_name": "Error Recovery and Resilience", "total_tests": 4, "passed": 2, "failed": 2, "success_rate": 0.5, "duration": 44.785319566726685, "results": [{"test_name": "LLM Process Health Monitoring", "success": true, "duration": 10.058173656463623, "error_message": null, "response_data": {"health_checks": 5, "available_checks": 5}, "status_code": null}, {"test_name": "Graceful Degradation", "success": true, "duration": 3.8504281044006348, "error_message": null, "response_data": {"normal_success": 3, "stress_success": 10, "health_after_stress": true}, "status_code": null}, {"test_name": "System Recovery After Errors", "success": false, "duration": 10.84093952178955, "error_message": "Normal operation failed after error scenarios: status=200, tokens=0", "response_data": null, "status_code": null}, {"test_name": "Request Timeout Handling", "success": false, "duration": 0.0, "error_message": "name 'aiohttp' is not defined", "response_data": null, "status_code": null}]}, {"suite_name": "Streaming Response", "total_tests": 3, "passed": 0, "failed": 3, "success_rate": 0.0, "duration": 21.940821647644043, "results": [{"test_name": "Real-time Token Streaming", "success": false, "duration": 3.1034247875213623, "error_message": "unsupported format string passed to NoneType.__format__", "response_data": null, "status_code": null}, {"test_name": "Concurrent Streaming Performance", "success": false, "duration": 3.5349111557006836, "error_message": "Concurrent streaming failed: success_rate=100.00%, first_token=0.000s, tokens/sec=0.00", "response_data": null, "status_code": null}, {"test_name": "No Buffering Delays", "success": false, "duration": 6.256798982620239, "error_message": "No token intervals measured", "response_data": null, "status_code": null}]}, {"suite_name": "Scalability and Stress", "total_tests": 3, "passed": 0, "failed": 3, "success_rate": 0.0, "duration": 186.48799753189087, "results": [{"test_name": "Moderate Concurrent Load (100 requests)", "success": false, "duration": 8.802917003631592, "error_message": "Success rate: 0.00%, Avg response time: 5.77s", "response_data": null, "status_code": null}, {"test_name": "High Concurrent Load (500 requests)", "success": false, "duration": 41.77009844779968, "error_message": "Success rate: 0.00%, Avg response time: 21.18s", "response_data": null, "status_code": null}, {"test_name": "Extreme Load (1000 requests)", "success": false, "duration": 105.87503290176392, "error_message": "Success rate too low: 0.00% (expected >70%)", "response_data": null, "status_code": null}]}], "test_config": {"base_url": "http://127.0.0.1:8000", "timeout": 30, "max_retries": 3, "test_collection": "test_collection", "stress_test_requests": 1000, "concurrent_test_requests": 100, "request_timeout": 60, "stream_timeout": 120, "health_check_interval": 5, "log_level": "INFO"}}