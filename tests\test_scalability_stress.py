# tests/test_scalability_stress.py
"""
Test scalability and stress handling for SecuraMind API

Tests:
1. 1000 concurrent request stress test
2. Response time measurement under load
3. Verify no requests are dropped or rejected
4. System stability under extreme load
"""

import asyncio
import time
import logging
import statistics
from typing import List, Dict, Any, Tuple
from .test_config import APIClient, TestR<PERSON>ult, TestLogger, TEST_CONFIG

logger = logging.getLogger(__name__)

class ScalabilityStressTests:
    """Test suite for scalability and stress testing"""
    
    def __init__(self):
        self.test_logger = TestLogger("tests/scalability_stress.log")
        self.results: List[TestResult] = []
        
    async def test_moderate_concurrent_load(self) -> TestResult:
        """Test with moderate concurrent load (100 requests)"""
        test_name = "Moderate Concurrent Load (100 requests)"
        self.test_logger.log_test_start(test_name)
        start_time = time.time()
        
        try:
            async with APIClient() as client:
                # Setup test collection
                collection_name = f"test_moderate_{int(time.time())}"
                status, data = await client.ingest_text(
                    collection_name,
                    "Test data for moderate load testing",
                    "moderate_test",
                    "moderate_1"
                )
                
                if status != 200:
                    raise Exception(f"Failed to ingest test data: {data}")
                
                await asyncio.sleep(3)
                
                # Create 100 concurrent requests
                num_requests = 100
                tasks = []
                request_start_times = []
                
                for i in range(num_requests):
                    request_start_times.append(time.time())
                    task = client.inference_request(collection_name, f"Moderate load query {i}")
                    tasks.append(task)
                
                # Execute all requests concurrently
                logger.info(f"Starting {num_requests} concurrent requests...")
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Analyze results
                success_count = 0
                error_count = 0
                timeout_count = 0
                response_times = []
                
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        error_count += 1
                        logger.error(f"Request {i} failed with exception: {result}")
                    else:
                        status_code, tokens = result
                        response_time = time.time() - request_start_times[i]
                        response_times.append(response_time)
                        
                        if status_code == 200 and len(tokens) > 0:
                            success_count += 1
                        elif status_code == 408:
                            timeout_count += 1
                        else:
                            error_count += 1
                            logger.error(f"Request {i} failed with status {status_code}")
                
                # Calculate statistics
                if response_times:
                    avg_response_time = statistics.mean(response_times)
                    median_response_time = statistics.median(response_times)
                    max_response_time = max(response_times)
                    min_response_time = min(response_times)
                else:
                    avg_response_time = median_response_time = max_response_time = min_response_time = 0
                
                logger.info(f"Results: {success_count} success, {error_count} errors, {timeout_count} timeouts")
                logger.info(f"Response times - Avg: {avg_response_time:.2f}s, Median: {median_response_time:.2f}s, Max: {max_response_time:.2f}s")
                
                # Success criteria: >90% success rate, reasonable response times
                success_rate = success_count / num_requests
                if success_rate >= 0.9 and avg_response_time < 60:
                    result = TestResult(
                        test_name, 
                        True, 
                        time.time() - start_time,
                        response_data={
                            "success_rate": success_rate,
                            "avg_response_time": avg_response_time,
                            "median_response_time": median_response_time,
                            "max_response_time": max_response_time
                        }
                    )
                else:
                    result = TestResult(
                        test_name,
                        False,
                        time.time() - start_time,
                        f"Success rate: {success_rate:.2%}, Avg response time: {avg_response_time:.2f}s"
                    )
                    
        except Exception as e:
            result = TestResult(test_name, False, time.time() - start_time, str(e))
            
        self.test_logger.log_test_result(result)
        self.test_logger.log_test_end(test_name)
        return result
        
    async def test_high_concurrent_load(self) -> TestResult:
        """Test with high concurrent load (500 requests)"""
        test_name = "High Concurrent Load (500 requests)"
        self.test_logger.log_test_start(test_name)
        start_time = time.time()
        
        try:
            async with APIClient() as client:
                # Setup test collection
                collection_name = f"test_high_{int(time.time())}"
                status, data = await client.ingest_text(
                    collection_name,
                    "Test data for high load testing",
                    "high_test",
                    "high_1"
                )
                
                if status != 200:
                    raise Exception(f"Failed to ingest test data: {data}")
                
                await asyncio.sleep(3)
                
                # Create 500 concurrent requests in batches to avoid overwhelming
                num_requests = 500
                batch_size = 50
                all_results = []
                all_start_times = []
                
                for batch_start in range(0, num_requests, batch_size):
                    batch_end = min(batch_start + batch_size, num_requests)
                    batch_tasks = []
                    batch_start_times = []
                    
                    logger.info(f"Starting batch {batch_start//batch_size + 1}: requests {batch_start}-{batch_end-1}")
                    
                    for i in range(batch_start, batch_end):
                        batch_start_times.append(time.time())
                        task = client.inference_request(collection_name, f"High load query {i}")
                        batch_tasks.append(task)
                    
                    # Execute batch
                    batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
                    all_results.extend(batch_results)
                    all_start_times.extend(batch_start_times)
                    
                    # Small delay between batches
                    await asyncio.sleep(1)
                
                # Analyze results
                success_count = 0
                error_count = 0
                timeout_count = 0
                response_times = []
                
                for i, result in enumerate(all_results):
                    if isinstance(result, Exception):
                        error_count += 1
                        logger.error(f"Request {i} failed with exception: {result}")
                    else:
                        status_code, tokens = result
                        response_time = time.time() - all_start_times[i]
                        response_times.append(response_time)
                        
                        if status_code == 200 and len(tokens) > 0:
                            success_count += 1
                        elif status_code == 408:
                            timeout_count += 1
                        else:
                            error_count += 1
                
                # Calculate statistics
                if response_times:
                    avg_response_time = statistics.mean(response_times)
                    median_response_time = statistics.median(response_times)
                    max_response_time = max(response_times)
                    p95_response_time = statistics.quantiles(response_times, n=20)[18]  # 95th percentile
                else:
                    avg_response_time = median_response_time = max_response_time = p95_response_time = 0
                
                logger.info(f"Results: {success_count} success, {error_count} errors, {timeout_count} timeouts")
                logger.info(f"Response times - Avg: {avg_response_time:.2f}s, Median: {median_response_time:.2f}s, 95th: {p95_response_time:.2f}s, Max: {max_response_time:.2f}s")
                
                # Success criteria: >80% success rate for high load
                success_rate = success_count / num_requests
                if success_rate >= 0.8 and avg_response_time < 120:
                    result = TestResult(
                        test_name,
                        True,
                        time.time() - start_time,
                        response_data={
                            "success_rate": success_rate,
                            "avg_response_time": avg_response_time,
                            "median_response_time": median_response_time,
                            "p95_response_time": p95_response_time,
                            "max_response_time": max_response_time
                        }
                    )
                else:
                    result = TestResult(
                        test_name,
                        False,
                        time.time() - start_time,
                        f"Success rate: {success_rate:.2%}, Avg response time: {avg_response_time:.2f}s"
                    )
                    
        except Exception as e:
            result = TestResult(test_name, False, time.time() - start_time, str(e))
            
        self.test_logger.log_test_result(result)
        self.test_logger.log_test_end(test_name)
        return result
        
    async def test_extreme_load_1000_requests(self) -> TestResult:
        """Test with extreme load (1000 requests) - the ultimate stress test"""
        test_name = "Extreme Load (1000 requests)"
        self.test_logger.log_test_start(test_name)
        start_time = time.time()
        
        try:
            async with APIClient() as client:
                # Setup test collection
                collection_name = f"test_extreme_{int(time.time())}"
                status, data = await client.ingest_text(
                    collection_name,
                    "Test data for extreme load testing",
                    "extreme_test",
                    "extreme_1"
                )
                
                if status != 200:
                    raise Exception(f"Failed to ingest test data: {data}")
                
                await asyncio.sleep(5)
                
                # Create 1000 requests in smaller batches
                num_requests = 1000
                batch_size = 25  # Smaller batches for extreme load
                all_results = []
                all_start_times = []
                
                logger.info(f"Starting extreme load test with {num_requests} requests in batches of {batch_size}")
                
                for batch_start in range(0, num_requests, batch_size):
                    batch_end = min(batch_start + batch_size, num_requests)
                    batch_tasks = []
                    batch_start_times = []
                    
                    if batch_start % 100 == 0:  # Log every 100 requests
                        logger.info(f"Progress: {batch_start}/{num_requests} requests started")
                    
                    for i in range(batch_start, batch_end):
                        batch_start_times.append(time.time())
                        task = client.inference_request(collection_name, f"Extreme load query {i}")
                        batch_tasks.append(task)
                    
                    # Execute batch
                    batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
                    all_results.extend(batch_results)
                    all_start_times.extend(batch_start_times)
                    
                    # Very small delay between batches
                    await asyncio.sleep(0.5)
                
                # Analyze results
                success_count = 0
                error_count = 0
                timeout_count = 0
                response_times = []
                
                for i, result in enumerate(all_results):
                    if isinstance(result, Exception):
                        error_count += 1
                        if i % 100 == 0:  # Log every 100th error to avoid spam
                            logger.error(f"Request {i} failed with exception: {result}")
                    else:
                        status_code, tokens = result
                        response_time = time.time() - all_start_times[i]
                        response_times.append(response_time)
                        
                        if status_code == 200 and len(tokens) > 0:
                            success_count += 1
                        elif status_code == 408:
                            timeout_count += 1
                        else:
                            error_count += 1
                
                # Calculate statistics
                if response_times:
                    avg_response_time = statistics.mean(response_times)
                    median_response_time = statistics.median(response_times)
                    max_response_time = max(response_times)
                    min_response_time = min(response_times)
                    if len(response_times) >= 20:
                        p95_response_time = statistics.quantiles(response_times, n=20)[18]
                        p99_response_time = statistics.quantiles(response_times, n=100)[98]
                    else:
                        p95_response_time = p99_response_time = max_response_time
                else:
                    avg_response_time = median_response_time = max_response_time = min_response_time = p95_response_time = p99_response_time = 0
                
                logger.info(f"EXTREME LOAD RESULTS:")
                logger.info(f"  Success: {success_count}, Errors: {error_count}, Timeouts: {timeout_count}")
                logger.info(f"  Response times - Avg: {avg_response_time:.2f}s, Median: {median_response_time:.2f}s")
                logger.info(f"  Response times - 95th: {p95_response_time:.2f}s, 99th: {p99_response_time:.2f}s, Max: {max_response_time:.2f}s")
                
                # Success criteria: >70% success rate for extreme load (more lenient)
                success_rate = success_count / num_requests
                if success_rate >= 0.7:
                    result = TestResult(
                        test_name,
                        True,
                        time.time() - start_time,
                        response_data={
                            "success_rate": success_rate,
                            "total_requests": num_requests,
                            "successful_requests": success_count,
                            "avg_response_time": avg_response_time,
                            "median_response_time": median_response_time,
                            "p95_response_time": p95_response_time,
                            "p99_response_time": p99_response_time,
                            "max_response_time": max_response_time
                        }
                    )
                else:
                    result = TestResult(
                        test_name,
                        False,
                        time.time() - start_time,
                        f"Success rate too low: {success_rate:.2%} (expected >70%)"
                    )
                    
        except Exception as e:
            result = TestResult(test_name, False, time.time() - start_time, str(e))
            
        self.test_logger.log_test_result(result)
        self.test_logger.log_test_end(test_name)
        return result
        
    async def run_all_tests(self) -> List[TestResult]:
        """Run all scalability and stress tests"""
        logger.info("Starting Scalability and Stress Tests")
        
        tests = [
            self.test_moderate_concurrent_load,
            self.test_high_concurrent_load,
            self.test_extreme_load_1000_requests
        ]
        
        for test_func in tests:
            result = await test_func()
            self.results.append(result)
            
            # Longer pause between stress tests to let system recover
            await asyncio.sleep(10)
            
        return self.results

# Main execution for standalone testing
async def main():
    """Run scalability stress tests standalone"""
    test_suite = ScalabilityStressTests()
    results = await test_suite.run_all_tests()
    
    # Summary
    passed = sum(1 for r in results if r.success)
    total = len(results)
    
    print(f"\n=== SCALABILITY STRESS TESTS SUMMARY ===")
    print(f"Passed: {passed}/{total}")
    print(f"Failed: {total - passed}/{total}")
    
    for result in results:
        status = "PASS" if result.success else "FAIL"
        print(f"  {status}: {result.test_name} ({result.duration:.2f}s)")
        if result.response_data:
            data = result.response_data
            if "success_rate" in data:
                print(f"    Success Rate: {data['success_rate']:.2%}")
                print(f"    Avg Response Time: {data['avg_response_time']:.2f}s")
        if not result.success:
            print(f"    Error: {result.error_message}")

if __name__ == "__main__":
    asyncio.run(main())
