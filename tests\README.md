# SecuraMind API Test Suite

Comprehensive testing framework for SecuraMind API functionality including concurrent request handling, thread safety, scalability, data isolation, error recovery, and streaming response verification.

## Test Coverage

### 1. Concurrent Request Handling (`test_concurrent_requests.py`)
- **LLM Routing**: Tests that first request gets main LLM, subsequent requests get backup LLM when main is busy
- **Request Queueing**: Verifies that when both LLMs are occupied, additional requests queue and wait (no timeouts/rejections)
- **Request Ordering**: Confirms requests are processed in proper order once LLM instances become available

### 2. Concurrent Operations (`test_concurrent_operations.py`)
- **Simultaneous Inference + Ingestion**: Tests that inference requests can be processed while ingestion operations are running
- **Unconsumed Streams**: Verifies multiple inference requests work correctly even when response streams are not consumed by clients
- **Mixed Workloads**: Ensures system handles mixed inference + ingestion workloads without conflicts

### 3. Scalability and Stress Testing (`test_scalability_stress.py`)
- **Moderate Load**: 100 concurrent requests with >90% success rate requirement
- **High Load**: 500 concurrent requests with >80% success rate requirement  
- **Extreme Load**: 1000 concurrent requests (ultimate stress test) with >70% success rate requirement
- **Performance Metrics**: Measures response times, tokens per second, and system stability

### 4. Data Isolation and Security (`test_data_isolation.py`)
- **User Request Isolation**: Tests that User A only receives responses for their own Request A
- **Session Isolation**: Verifies no data leakage between concurrent user sessions
- **Multi-User Testing**: Tests with 20+ concurrent users to ensure proper isolation at scale

### 5. Thread Safety (`test_thread_safety.py`)
- **LLM Instance Locking**: Verifies that LLM instances are never accessed by multiple requests simultaneously
- **Concurrent Access Prevention**: Tests the locking mechanism prevents concurrent access to same LLM instance
- **GGML_ASSERT Prevention**: Validates that thread safety implementation prevents GGML_ASSERT failures

### 6. Error Recovery and Resilience (`test_error_recovery.py`)
- **Health Monitoring**: Tests LLM process health monitoring and reporting
- **Graceful Degradation**: Verifies system continues operating when one LLM becomes unavailable
- **Error Recovery**: Tests system recovery from various error conditions
- **Timeout Handling**: Verifies proper handling of request timeouts and cleanup

### 7. Streaming Response Verification (`test_streaming.py`)
- **Real-time Streaming**: Tests that all responses are streamed token-by-token in real-time
- **Concurrent Streaming**: Verifies streaming works correctly under concurrent load
- **No Buffering**: Ensures no artificial buffering delays in token delivery to clients

## Quick Start

### Prerequisites
1. SecuraMind server must be running on `http://127.0.0.1:8000`
2. Both main and backup LLMs should be available for comprehensive testing
3. Python dependencies installed (see `requirements.txt`)

### Running All Tests
```bash
# Run the complete test suite
python tests/run_all_tests.py
```

### Running Individual Test Suites
```bash
# Test concurrent request handling
python -m tests.test_concurrent_requests

# Test scalability and stress
python -m tests.test_scalability_stress

# Test data isolation
python -m tests.test_data_isolation

# Test thread safety
python -m tests.test_thread_safety

# Test error recovery
python -m tests.test_error_recovery

# Test streaming responses
python -m tests.test_streaming

# Test concurrent operations
python -m tests.test_concurrent_operations
```

## Test Configuration

Edit `tests/test_config.py` to modify test parameters:

```python
TEST_CONFIG = {
    "base_url": "http://127.0.0.1:8000",
    "timeout": 30,
    "stress_test_requests": 1000,
    "concurrent_test_requests": 100,
    # ... other settings
}
```

## Test Results and Logs

### Log Files
- `tests/master_test_results.log` - Master test runner logs
- `tests/master_test_execution.log` - Detailed execution logs
- `tests/concurrent_requests.log` - Concurrent request test logs
- `tests/scalability_stress.log` - Stress test logs
- `tests/data_isolation.log` - Data isolation test logs
- `tests/thread_safety.log` - Thread safety test logs
- `tests/error_recovery.log` - Error recovery test logs
- `tests/streaming.log` - Streaming test logs
- `tests/concurrent_operations.log` - Concurrent operations logs

### Result Files
- `tests/detailed_test_results.json` - Complete test results in JSON format

## Understanding Test Results

### Success Criteria

**Concurrent Request Handling:**
- All requests should complete successfully
- No GGML_ASSERT failures
- Proper LLM routing (main → backup)

**Scalability:**
- 100 requests: >90% success rate
- 500 requests: >80% success rate  
- 1000 requests: >70% success rate

**Data Isolation:**
- Zero data leakage between users
- Each user receives only their own data

**Thread Safety:**
- Zero GGML_ASSERT errors
- No concurrent access violations

**Streaming:**
- Real-time token delivery
- No artificial buffering delays
- Consistent performance under load

### Performance Benchmarks

**Response Times:**
- Time to first token: <10 seconds (normal load), <15 seconds (high load)
- Average response time: <60 seconds (moderate load), <120 seconds (high load)

**Throughput:**
- Tokens per second: >0.5 (normal), >0.3 (under load)
- Concurrent capacity: 2+ simultaneous requests (main + backup LLM)

## Troubleshooting

### Common Issues

1. **Server Not Ready**
   ```
   Error: Server is not ready
   Solution: Start SecuraMind server first: python -m securamind_core.main
   ```

2. **LLM Not Available**
   ```
   Error: No LLM processes are currently available
   Solution: Check LLM model files are in models/ directory
   ```

3. **High Failure Rate**
   ```
   Error: Success rate <70%
   Solution: Check server resources, reduce concurrent load, verify model files
   ```

4. **GGML_ASSERT Errors**
   ```
   Error: GGML_ASSERT failures detected
   Solution: Thread safety issue - check LLM process isolation
   ```

### Test Environment Requirements

- **Memory**: 8GB+ RAM recommended for stress testing
- **CPU**: Multi-core processor for concurrent operations
- **Storage**: SSD recommended for model loading performance
- **Network**: Local testing (127.0.0.1) to avoid network latency

## Development and Customization

### Adding New Tests

1. Create new test file in `tests/` directory
2. Follow the pattern of existing test files
3. Use `TestResult` and `TestLogger` from `test_config.py`
4. Add to `run_all_tests.py` test suite list

### Test Structure

```python
from tests.test_config import APIClient, TestResult, TestLogger

class MyTests:
    def __init__(self):
        self.test_logger = TestLogger("tests/my_tests.log")
        self.results = []
    
    async def test_something(self) -> TestResult:
        test_name = "My Test"
        start_time = time.time()
        
        try:
            async with APIClient() as client:
                # Test implementation
                pass
            
            result = TestResult(test_name, True, time.time() - start_time)
        except Exception as e:
            result = TestResult(test_name, False, time.time() - start_time, str(e))
        
        self.test_logger.log_test_result(result)
        return result
```

## Continuous Integration

The test suite is designed for CI/CD integration:

```bash
# CI script example
python tests/run_all_tests.py
exit_code=$?

if [ $exit_code -eq 0 ]; then
    echo "All tests passed"
else
    echo "Tests failed"
    exit 1
fi
```

## Performance Monitoring

Use the test suite for ongoing performance monitoring:

1. Run tests regularly against development builds
2. Monitor success rates and response times
3. Set up alerts for degraded performance
4. Use detailed JSON results for trend analysis
