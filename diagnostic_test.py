#!/usr/bin/env python3
"""
Diagnostic test to understand the core issues with SecuraMind API
"""

import asyncio
import aiohttp
import json
import time

async def test_basic_functionality():
    """Test basic API functionality to identify core issues"""
    
    print("=== SecuraMind Diagnostic Test ===")
    
    base_url = "http://127.0.0.1:8000"
    
    async with aiohttp.ClientSession() as session:
        # 1. Test health endpoint
        print("\n1. Testing health endpoint...")
        try:
            async with session.get(f"{base_url}/api/v1/health") as response:
                health_data = await response.json()
                print(f"   Status: {response.status}")
                print(f"   Main LLM: {health_data.get('main_llm_status')} (Available: {health_data.get('main_llm_available')})")
                print(f"   Backup LLM: {health_data.get('backup_llm_status')} (Available: {health_data.get('backup_llm_available')})")
                print(f"   Concurrent capacity: {health_data.get('concurrent_capacity')}")
        except Exception as e:
            print(f"   ERROR: {e}")
            return
        
        # 2. Test ingestion
        print("\n2. Testing text ingestion...")
        collection_name = f"diagnostic_test_{int(time.time())}"
        ingestion_payload = {
            "collection_name": collection_name,
            "text_content": "This is a diagnostic test document for SecuraMind.",
            "source_name": "diagnostic_test",
            "source_identifier": "diag_1"
        }
        
        try:
            async with session.post(f"{base_url}/api/v1/ingest/text", json=ingestion_payload) as response:
                ingestion_data = await response.json()
                print(f"   Status: {response.status}")
                print(f"   Response: {ingestion_data}")
        except Exception as e:
            print(f"   ERROR: {e}")
            return
        
        # Wait for ingestion to complete
        print("   Waiting 5 seconds for ingestion to complete...")
        await asyncio.sleep(5)
        
        # 3. Test inference
        print("\n3. Testing inference...")
        inference_payload = {
            "collection_name": collection_name,
            "query": "What is this document about?",
            "language": "en"
        }
        
        try:
            async with session.post(f"{base_url}/api/v1/inference", json=inference_payload) as response:
                print(f"   Status: {response.status}")
                print(f"   Content-Type: {response.headers.get('content-type')}")
                
                if response.status == 200:
                    print("   Streaming response:")
                    token_count = 0
                    async for line in response.content:
                        line = line.decode('utf-8').strip()
                        if line.startswith('data: '):
                            try:
                                data = json.loads(line[6:])
                                if 'token' in data:
                                    token_count += 1
                                    if token_count <= 5:  # Show first 5 tokens
                                        print(f"     Token {token_count}: '{data['token']}'")
                                elif 'event' in data:
                                    print(f"     Event: {data}")
                                    if data.get('event') == 'eos':
                                        break
                            except json.JSONDecodeError as e:
                                print(f"     JSON decode error: {e}")
                                print(f"     Raw line: {line}")
                    
                    print(f"   Total tokens received: {token_count}")
                else:
                    error_text = await response.text()
                    print(f"   Error response: {error_text}")
                    
        except Exception as e:
            print(f"   ERROR: {e}")
        
        # 4. Test with nonexistent collection
        print("\n4. Testing with nonexistent collection...")
        try:
            nonexistent_payload = {
                "collection_name": "nonexistent_collection_12345",
                "query": "Test query",
                "language": "en"
            }
            
            async with session.post(f"{base_url}/api/v1/inference", json=nonexistent_payload) as response:
                print(f"   Status: {response.status}")
                
                if response.status == 200:
                    token_count = 0
                    async for line in response.content:
                        line = line.decode('utf-8').strip()
                        if line.startswith('data: '):
                            try:
                                data = json.loads(line[6:])
                                if 'token' in data:
                                    token_count += 1
                                elif 'event' in data and data.get('event') == 'eos':
                                    break
                            except json.JSONDecodeError:
                                continue
                    print(f"   Tokens received: {token_count}")
                else:
                    error_text = await response.text()
                    print(f"   Error response: {error_text}")
                    
        except Exception as e:
            print(f"   ERROR: {e}")

if __name__ == "__main__":
    asyncio.run(test_basic_functionality())
