# tests/test_data_isolation.py
"""
Test data isolation and security for SecuraMind API

Tests:
1. User request isolation - User A only gets responses for Request A
2. No data leakage between concurrent user sessions
3. Multi-user concurrent testing with proper isolation
"""

import asyncio
import time
import logging
import uuid
from typing import List, Dict, Any, Tuple
from .test_config import APIClient, TestResult, TestLogger, TEST_CONFIG

logger = logging.getLogger(__name__)

class DataIsolationTests:
    """Test suite for data isolation and security"""
    
    def __init__(self):
        self.test_logger = TestLogger("tests/data_isolation.log")
        self.results: List[TestResult] = []
        
    async def test_user_request_isolation(self) -> TestResult:
        """Test that each user only receives responses for their own requests"""
        test_name = "User Request Isolation"
        self.test_logger.log_test_start(test_name)
        start_time = time.time()
        
        try:
            # Create separate collections for different "users"
            user_collections = {}
            user_data = {
                "user_a": {
                    "secret": "apple",
                    "data": "User A's secret codename is apple. This is confidential information for User A only.",
                    "query": "What is my secret codename?"
                },
                "user_b": {
                    "secret": "banana", 
                    "data": "User B's secret codename is banana. This is confidential information for User B only.",
                    "query": "What is my secret codename?"
                },
                "user_c": {
                    "secret": "cherry",
                    "data": "User C's secret codename is cherry. This is confidential information for User C only.", 
                    "query": "What is my secret codename?"
                }
            }
            
            # Setup collections for each user
            async with APIClient() as client:
                for user_id, data in user_data.items():
                    collection_name = f"test_isolation_{user_id}_{int(time.time())}"
                    user_collections[user_id] = collection_name
                    
                    status, response = await client.ingest_text(
                        collection_name,
                        data["data"],
                        f"{user_id}_source",
                        f"{user_id}_doc"
                    )
                    
                    if status != 200:
                        raise Exception(f"Failed to ingest data for {user_id}: {response}")
                
                # Wait for ingestion to complete
                await asyncio.sleep(5)
                
                # Send concurrent queries from different "users"
                tasks = []
                user_order = []
                
                for user_id, data in user_data.items():
                    collection_name = user_collections[user_id]
                    task = client.inference_request(collection_name, data["query"])
                    tasks.append(task)
                    user_order.append(user_id)
                
                # Execute all queries concurrently
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Verify each user gets their own data
                isolation_success = True
                isolation_errors = []
                
                for i, (user_id, result) in enumerate(zip(user_order, results)):
                    if isinstance(result, Exception):
                        isolation_errors.append(f"User {user_id} request failed: {result}")
                        isolation_success = False
                        continue
                    
                    status_code, tokens = result
                    if status_code != 200:
                        isolation_errors.append(f"User {user_id} got status {status_code}")
                        isolation_success = False
                        continue
                    
                    # Check if response contains user's own secret
                    response_text = "".join(tokens).lower()
                    user_secret = user_data[user_id]["secret"].lower()
                    
                    # Verify user gets their own secret
                    if user_secret not in response_text:
                        isolation_errors.append(f"User {user_id} didn't get their secret '{user_secret}'")
                        isolation_success = False
                    
                    # Verify user doesn't get other users' secrets
                    for other_user, other_data in user_data.items():
                        if other_user != user_id:
                            other_secret = other_data["secret"].lower()
                            if other_secret in response_text:
                                isolation_errors.append(f"User {user_id} got {other_user}'s secret '{other_secret}'")
                                isolation_success = False
                    
                    logger.info(f"User {user_id} response: {response_text[:100]}...")
                
                if isolation_success:
                    result = TestResult(test_name, True, time.time() - start_time)
                else:
                    result = TestResult(
                        test_name,
                        False,
                        time.time() - start_time,
                        f"Isolation failures: {'; '.join(isolation_errors)}"
                    )
                    
        except Exception as e:
            result = TestResult(test_name, False, time.time() - start_time, str(e))
            
        self.test_logger.log_test_result(result)
        self.test_logger.log_test_end(test_name)
        return result
        
    async def test_concurrent_session_isolation(self) -> TestResult:
        """Test no data leakage between concurrent user sessions"""
        test_name = "Concurrent Session Isolation"
        self.test_logger.log_test_start(test_name)
        start_time = time.time()
        
        try:
            # Create multiple concurrent "sessions" with different data
            session_data = {}
            session_collections = {}
            
            # Generate unique session data
            for i in range(5):
                session_id = f"session_{i}"
                unique_id = str(uuid.uuid4())[:8]
                session_data[session_id] = {
                    "unique_id": unique_id,
                    "data": f"Session {session_id} unique identifier is {unique_id}. This data belongs only to {session_id}.",
                    "query": f"What is the unique identifier for {session_id}?"
                }
            
            async with APIClient() as client:
                # Setup collections for each session
                for session_id, data in session_data.items():
                    collection_name = f"test_session_{session_id}_{int(time.time())}"
                    session_collections[session_id] = collection_name
                    
                    status, response = await client.ingest_text(
                        collection_name,
                        data["data"],
                        f"{session_id}_source",
                        f"{session_id}_doc"
                    )
                    
                    if status != 200:
                        raise Exception(f"Failed to ingest data for {session_id}: {response}")
                
                await asyncio.sleep(5)
                
                # Send multiple rounds of concurrent queries to stress test isolation
                for round_num in range(3):
                    logger.info(f"Starting isolation test round {round_num + 1}")
                    
                    tasks = []
                    session_order = []
                    
                    for session_id, data in session_data.items():
                        collection_name = session_collections[session_id]
                        task = client.inference_request(collection_name, data["query"])
                        tasks.append(task)
                        session_order.append(session_id)
                    
                    # Execute concurrently
                    results = await asyncio.gather(*tasks, return_exceptions=True)
                    
                    # Verify isolation for this round
                    for i, (session_id, result) in enumerate(zip(session_order, results)):
                        if isinstance(result, Exception):
                            raise Exception(f"Session {session_id} round {round_num} failed: {result}")
                        
                        status_code, tokens = result
                        if status_code != 200:
                            raise Exception(f"Session {session_id} round {round_num} got status {status_code}")
                        
                        response_text = "".join(tokens).lower()
                        session_unique_id = session_data[session_id]["unique_id"].lower()
                        
                        # Verify session gets its own data
                        if session_unique_id not in response_text:
                            raise Exception(f"Session {session_id} round {round_num} missing own ID {session_unique_id}")
                        
                        # Verify session doesn't get other sessions' data
                        for other_session, other_data in session_data.items():
                            if other_session != session_id:
                                other_unique_id = other_data["unique_id"].lower()
                                if other_unique_id in response_text:
                                    raise Exception(f"Session {session_id} round {round_num} leaked {other_session}'s ID {other_unique_id}")
                    
                    # Small delay between rounds
                    await asyncio.sleep(1)
                
                result = TestResult(test_name, True, time.time() - start_time)
                
        except Exception as e:
            result = TestResult(test_name, False, time.time() - start_time, str(e))
            
        self.test_logger.log_test_result(result)
        self.test_logger.log_test_end(test_name)
        return result
        
    async def test_multi_user_concurrent_isolation(self) -> TestResult:
        """Test proper isolation with many concurrent users"""
        test_name = "Multi-User Concurrent Isolation"
        self.test_logger.log_test_start(test_name)
        start_time = time.time()
        
        try:
            # Create many "users" with sensitive data
            num_users = 20
            user_data = {}
            user_collections = {}
            
            for i in range(num_users):
                user_id = f"user_{i:02d}"
                sensitive_info = f"SENSITIVE_{i:03d}_{uuid.uuid4().hex[:8]}"
                user_data[user_id] = {
                    "sensitive_info": sensitive_info,
                    "data": f"User {user_id} has sensitive information: {sensitive_info}. This must remain private.",
                    "query": "What is my sensitive information?"
                }
            
            async with APIClient() as client:
                # Setup collections for all users
                ingestion_tasks = []
                for user_id, data in user_data.items():
                    collection_name = f"test_multi_{user_id}_{int(time.time())}"
                    user_collections[user_id] = collection_name
                    
                    task = client.ingest_text(
                        collection_name,
                        data["data"],
                        f"{user_id}_source",
                        f"{user_id}_doc"
                    )
                    ingestion_tasks.append((user_id, task))
                
                # Execute all ingestions concurrently
                ingestion_results = await asyncio.gather(*[task for _, task in ingestion_tasks], return_exceptions=True)
                
                # Verify all ingestions succeeded
                for i, (user_id, result) in enumerate(zip([uid for uid, _ in ingestion_tasks], ingestion_results)):
                    if isinstance(result, Exception):
                        raise Exception(f"Ingestion failed for {user_id}: {result}")
                    
                    status, response = result
                    if status != 200:
                        raise Exception(f"Ingestion failed for {user_id} with status {status}: {response}")
                
                await asyncio.sleep(10)  # Wait for all ingestions to complete
                
                # Send concurrent queries from all users
                query_tasks = []
                user_order = []
                
                for user_id, data in user_data.items():
                    collection_name = user_collections[user_id]
                    task = client.inference_request(collection_name, data["query"])
                    query_tasks.append(task)
                    user_order.append(user_id)
                
                # Execute all queries concurrently
                logger.info(f"Executing {len(query_tasks)} concurrent queries for isolation test")
                query_results = await asyncio.gather(*query_tasks, return_exceptions=True)
                
                # Verify isolation for all users
                isolation_violations = []
                successful_users = 0
                
                for i, (user_id, result) in enumerate(zip(user_order, query_results)):
                    if isinstance(result, Exception):
                        isolation_violations.append(f"User {user_id} query failed: {result}")
                        continue
                    
                    status_code, tokens = result
                    if status_code != 200:
                        isolation_violations.append(f"User {user_id} got status {status_code}")
                        continue
                    
                    response_text = "".join(tokens).lower()
                    user_sensitive = user_data[user_id]["sensitive_info"].lower()
                    
                    # Check if user got their own sensitive info
                    if user_sensitive not in response_text:
                        isolation_violations.append(f"User {user_id} missing own sensitive info")
                        continue
                    
                    # Check for leakage of other users' sensitive info
                    leaked_info = []
                    for other_user, other_data in user_data.items():
                        if other_user != user_id:
                            other_sensitive = other_data["sensitive_info"].lower()
                            if other_sensitive in response_text:
                                leaked_info.append(f"{other_user}:{other_sensitive}")
                    
                    if leaked_info:
                        isolation_violations.append(f"User {user_id} leaked: {', '.join(leaked_info)}")
                        continue
                    
                    successful_users += 1
                
                # Success criteria: all users properly isolated
                if len(isolation_violations) == 0:
                    result = TestResult(
                        test_name,
                        True,
                        time.time() - start_time,
                        response_data={"successful_users": successful_users, "total_users": num_users}
                    )
                else:
                    result = TestResult(
                        test_name,
                        False,
                        time.time() - start_time,
                        f"Isolation violations: {'; '.join(isolation_violations[:5])}..."  # Limit error message length
                    )
                    
        except Exception as e:
            result = TestResult(test_name, False, time.time() - start_time, str(e))
            
        self.test_logger.log_test_result(result)
        self.test_logger.log_test_end(test_name)
        return result
        
    async def run_all_tests(self) -> List[TestResult]:
        """Run all data isolation tests"""
        logger.info("Starting Data Isolation Tests")
        
        tests = [
            self.test_user_request_isolation,
            self.test_concurrent_session_isolation,
            self.test_multi_user_concurrent_isolation
        ]
        
        for test_func in tests:
            result = await test_func()
            self.results.append(result)
            
            # Pause between tests
            await asyncio.sleep(3)
            
        return self.results

# Main execution for standalone testing
async def main():
    """Run data isolation tests standalone"""
    test_suite = DataIsolationTests()
    results = await test_suite.run_all_tests()
    
    # Summary
    passed = sum(1 for r in results if r.success)
    total = len(results)
    
    print(f"\n=== DATA ISOLATION TESTS SUMMARY ===")
    print(f"Passed: {passed}/{total}")
    print(f"Failed: {total - passed}/{total}")
    
    for result in results:
        status = "PASS" if result.success else "FAIL"
        print(f"  {status}: {result.test_name} ({result.duration:.2f}s)")
        if not result.success:
            print(f"    Error: {result.error_message}")

if __name__ == "__main__":
    asyncio.run(main())
