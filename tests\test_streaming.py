# tests/test_streaming.py
"""
Test streaming response functionality for SecuraMind API

Tests:
1. Real-time token streaming verification
2. Streaming performance under concurrent load
3. No buffering delays in token delivery
"""

import asyncio
import time
import logging
import json
from typing import List, Dict, Any, Tuple
from .test_config import APIClient, TestR<PERSON>ult, TestLogger, TEST_CONFIG

logger = logging.getLogger(__name__)

class StreamingTests:
    """Test suite for streaming response verification"""
    
    def __init__(self):
        self.test_logger = TestLogger("tests/streaming.log")
        self.results: List[TestResult] = []
        
    async def test_real_time_token_streaming(self) -> TestResult:
        """Test that tokens are streamed in real-time as they are generated"""
        test_name = "Real-time Token Streaming"
        self.test_logger.log_test_start(test_name)
        start_time = time.time()
        
        try:
            async with APIClient() as client:
                # Setup test collection
                collection_name = f"test_streaming_{int(time.time())}"
                status, data = await client.ingest_text(
                    collection_name,
                    "Test data for real-time streaming verification. This should generate a reasonable response.",
                    "streaming_test",
                    "stream_1"
                )
                
                if status != 200:
                    raise Exception(f"Failed to ingest test data: {data}")
                
                await asyncio.sleep(3)
                
                # Custom streaming client to measure timing
                async def timed_streaming_request(collection_name: str, query: str) -> Dict[str, Any]:
                    """Make streaming request with detailed timing measurements"""
                    payload = {
                        "collection_name": collection_name,
                        "query": query,
                        "language": "en"
                    }
                    
                    request_start = time.time()
                    first_token_time = None
                    last_token_time = None
                    tokens = []
                    token_times = []
                    
                    try:
                        async with client.session.post(
                            f"{client.base_url}/api/v1/inference",
                            json=payload
                        ) as response:
                            
                            if response.status != 200:
                                return {
                                    "success": False,
                                    "error": f"HTTP {response.status}",
                                    "tokens": [],
                                    "timing": {}
                                }
                            
                            async for line in response.content:
                                line = line.decode('utf-8').strip()
                                if line.startswith('data: '):
                                    try:
                                        data = json.loads(line[6:])
                                        if 'token' in data:
                                            token_time = time.time()
                                            if first_token_time is None:
                                                first_token_time = token_time
                                            last_token_time = token_time
                                            
                                            tokens.append(data['token'])
                                            token_times.append(token_time - request_start)
                                            
                                        elif 'event' in data and data['event'] == 'eos':
                                            break
                                    except json.JSONDecodeError:
                                        continue
                                        
                    except Exception as e:
                        return {
                            "success": False,
                            "error": str(e),
                            "tokens": tokens,
                            "timing": {}
                        }
                    
                    total_time = time.time() - request_start
                    time_to_first_token = first_token_time - request_start if first_token_time else None
                    
                    return {
                        "success": True,
                        "tokens": tokens,
                        "timing": {
                            "total_time": total_time,
                            "time_to_first_token": time_to_first_token,
                            "token_count": len(tokens),
                            "tokens_per_second": len(tokens) / total_time if total_time > 0 else 0,
                            "token_times": token_times
                        }
                    }
                
                # Test streaming with different query complexities
                test_queries = [
                    "What is this about?",
                    "Please provide a detailed explanation of the content.",
                    "Can you summarize the main points and provide additional context?"
                ]
                
                streaming_results = []
                
                for i, query in enumerate(test_queries):
                    logger.info(f"Testing streaming with query {i+1}: {query[:50]}...")
                    
                    result = await timed_streaming_request(collection_name, query)
                    streaming_results.append(result)
                    
                    if result["success"]:
                        timing = result["timing"]
                        logger.info(f"Query {i+1} results:")
                        logger.info(f"  Tokens: {timing['token_count']}")
                        logger.info(f"  Time to first token: {timing['time_to_first_token']:.3f}s")
                        logger.info(f"  Total time: {timing['total_time']:.3f}s")
                        logger.info(f"  Tokens/sec: {timing['tokens_per_second']:.2f}")
                    else:
                        logger.error(f"Query {i+1} failed: {result['error']}")
                    
                    # Brief pause between queries
                    await asyncio.sleep(1)
                
                # Analyze streaming performance
                successful_streams = [r for r in streaming_results if r["success"]]
                
                if len(successful_streams) == 0:
                    raise Exception("No streaming requests succeeded")
                
                # Calculate streaming metrics
                avg_time_to_first_token = sum(r["timing"]["time_to_first_token"] 
                                            for r in successful_streams) / len(successful_streams)
                avg_tokens_per_second = sum(r["timing"]["tokens_per_second"] 
                                          for r in successful_streams) / len(successful_streams)
                total_tokens = sum(r["timing"]["token_count"] for r in successful_streams)
                
                logger.info(f"Streaming performance summary:")
                logger.info(f"  Successful streams: {len(successful_streams)}/{len(test_queries)}")
                logger.info(f"  Avg time to first token: {avg_time_to_first_token:.3f}s")
                logger.info(f"  Avg tokens per second: {avg_tokens_per_second:.2f}")
                logger.info(f"  Total tokens streamed: {total_tokens}")
                
                # Success criteria: Fast first token, reasonable streaming speed
                if (len(successful_streams) == len(test_queries) and 
                    avg_time_to_first_token < 10.0 and  # First token within 10 seconds
                    avg_tokens_per_second > 0.5):  # At least 0.5 tokens per second
                    
                    result = TestResult(
                        test_name,
                        True,
                        time.time() - start_time,
                        response_data={
                            "successful_streams": len(successful_streams),
                            "avg_time_to_first_token": avg_time_to_first_token,
                            "avg_tokens_per_second": avg_tokens_per_second,
                            "total_tokens": total_tokens
                        }
                    )
                else:
                    result = TestResult(
                        test_name,
                        False,
                        time.time() - start_time,
                        f"Streaming performance insufficient: {len(successful_streams)}/{len(test_queries)} successful, "
                        f"first_token={avg_time_to_first_token:.3f}s, tokens/sec={avg_tokens_per_second:.2f}"
                    )
                    
        except Exception as e:
            result = TestResult(test_name, False, time.time() - start_time, str(e))
            
        self.test_logger.log_test_result(result)
        self.test_logger.log_test_end(test_name)
        return result
        
    async def test_concurrent_streaming_performance(self) -> TestResult:
        """Test streaming performance under concurrent load"""
        test_name = "Concurrent Streaming Performance"
        self.test_logger.log_test_start(test_name)
        start_time = time.time()
        
        try:
            async with APIClient() as client:
                # Setup test collection
                collection_name = f"test_concurrent_stream_{int(time.time())}"
                status, data = await client.ingest_text(
                    collection_name,
                    "Test data for concurrent streaming performance testing. This content should generate meaningful responses for multiple concurrent users.",
                    "concurrent_stream_test",
                    "concurrent_1"
                )
                
                if status != 200:
                    raise Exception(f"Failed to ingest test data: {data}")
                
                await asyncio.sleep(3)
                
                # Create multiple concurrent streaming requests
                num_concurrent = 8
                concurrent_tasks = []
                
                logger.info(f"Starting {num_concurrent} concurrent streaming requests")
                
                for i in range(num_concurrent):
                    # Use the timed streaming function from previous test
                    async def timed_concurrent_stream(request_id: int):
                        query = f"Concurrent streaming test query {request_id}"
                        
                        payload = {
                            "collection_name": collection_name,
                            "query": query,
                            "language": "en"
                        }
                        
                        request_start = time.time()
                        first_token_time = None
                        tokens = []
                        
                        try:
                            async with client.session.post(
                                f"{client.base_url}/api/v1/inference",
                                json=payload
                            ) as response:
                                
                                if response.status != 200:
                                    return {
                                        "request_id": request_id,
                                        "success": False,
                                        "error": f"HTTP {response.status}"
                                    }
                                
                                async for line in response.content:
                                    line = line.decode('utf-8').strip()
                                    if line.startswith('data: '):
                                        try:
                                            data = json.loads(line[6:])
                                            if 'token' in data:
                                                if first_token_time is None:
                                                    first_token_time = time.time()
                                                tokens.append(data['token'])
                                            elif 'event' in data and data['event'] == 'eos':
                                                break
                                        except json.JSONDecodeError:
                                            continue
                                            
                        except Exception as e:
                            return {
                                "request_id": request_id,
                                "success": False,
                                "error": str(e)
                            }
                        
                        total_time = time.time() - request_start
                        time_to_first_token = first_token_time - request_start if first_token_time else None
                        
                        return {
                            "request_id": request_id,
                            "success": True,
                            "tokens": len(tokens),
                            "total_time": total_time,
                            "time_to_first_token": time_to_first_token,
                            "tokens_per_second": len(tokens) / total_time if total_time > 0 else 0
                        }
                    
                    task = timed_concurrent_stream(i)
                    concurrent_tasks.append(task)
                
                # Execute all concurrent streaming requests
                concurrent_results = await asyncio.gather(*concurrent_tasks, return_exceptions=True)
                
                # Analyze concurrent streaming results
                successful_concurrent = []
                failed_concurrent = []
                
                for result in concurrent_results:
                    if isinstance(result, Exception):
                        failed_concurrent.append(f"Exception: {result}")
                    elif result.get("success"):
                        successful_concurrent.append(result)
                    else:
                        failed_concurrent.append(f"Request {result.get('request_id', 'unknown')}: {result.get('error', 'unknown error')}")
                
                if len(successful_concurrent) == 0:
                    raise Exception(f"No concurrent streams succeeded. Failures: {failed_concurrent}")
                
                # Calculate concurrent streaming metrics
                avg_concurrent_time_to_first = sum(r["time_to_first_token"] for r in successful_concurrent 
                                                 if r["time_to_first_token"]) / len(successful_concurrent)
                avg_concurrent_tokens_per_sec = sum(r["tokens_per_second"] for r in successful_concurrent) / len(successful_concurrent)
                total_concurrent_tokens = sum(r["tokens"] for r in successful_concurrent)
                
                logger.info(f"Concurrent streaming results:")
                logger.info(f"  Successful: {len(successful_concurrent)}/{num_concurrent}")
                logger.info(f"  Failed: {len(failed_concurrent)}")
                logger.info(f"  Avg time to first token: {avg_concurrent_time_to_first:.3f}s")
                logger.info(f"  Avg tokens per second: {avg_concurrent_tokens_per_sec:.2f}")
                logger.info(f"  Total tokens: {total_concurrent_tokens}")
                
                # Success criteria: Most concurrent streams succeed with reasonable performance
                success_rate = len(successful_concurrent) / num_concurrent
                if (success_rate >= 0.75 and  # At least 75% success rate
                    avg_concurrent_time_to_first < 15.0 and  # First token within 15 seconds under load
                    avg_concurrent_tokens_per_sec > 0.3):  # At least 0.3 tokens/sec under load
                    
                    result = TestResult(
                        test_name,
                        True,
                        time.time() - start_time,
                        response_data={
                            "concurrent_requests": num_concurrent,
                            "successful_streams": len(successful_concurrent),
                            "success_rate": success_rate,
                            "avg_time_to_first_token": avg_concurrent_time_to_first,
                            "avg_tokens_per_second": avg_concurrent_tokens_per_sec,
                            "total_tokens": total_concurrent_tokens
                        }
                    )
                else:
                    result = TestResult(
                        test_name,
                        False,
                        time.time() - start_time,
                        f"Concurrent streaming failed: success_rate={success_rate:.2%}, "
                        f"first_token={avg_concurrent_time_to_first:.3f}s, tokens/sec={avg_concurrent_tokens_per_sec:.2f}"
                    )
                    
        except Exception as e:
            result = TestResult(test_name, False, time.time() - start_time, str(e))
            
        self.test_logger.log_test_result(result)
        self.test_logger.log_test_end(test_name)
        return result
        
    async def test_no_buffering_delays(self) -> TestResult:
        """Test that there are no artificial buffering delays in token delivery"""
        test_name = "No Buffering Delays"
        self.test_logger.log_test_start(test_name)
        start_time = time.time()
        
        try:
            async with APIClient() as client:
                # Setup test collection
                collection_name = f"test_buffering_{int(time.time())}"
                status, data = await client.ingest_text(
                    collection_name,
                    "Test data for buffering delay verification. This should stream tokens without artificial delays.",
                    "buffering_test",
                    "buffer_1"
                )
                
                if status != 200:
                    raise Exception(f"Failed to ingest test data: {data}")
                
                await asyncio.sleep(3)
                
                # Test for consistent token delivery timing
                async def measure_token_intervals(query: str) -> Dict[str, Any]:
                    """Measure intervals between token deliveries"""
                    payload = {
                        "collection_name": collection_name,
                        "query": query,
                        "language": "en"
                    }
                    
                    token_timestamps = []
                    tokens = []
                    
                    try:
                        async with client.session.post(
                            f"{client.base_url}/api/v1/inference",
                            json=payload
                        ) as response:
                            
                            if response.status != 200:
                                return {"success": False, "error": f"HTTP {response.status}"}
                            
                            async for line in response.content:
                                line = line.decode('utf-8').strip()
                                if line.startswith('data: '):
                                    try:
                                        data = json.loads(line[6:])
                                        if 'token' in data:
                                            token_timestamps.append(time.time())
                                            tokens.append(data['token'])
                                        elif 'event' in data and data['event'] == 'eos':
                                            break
                                    except json.JSONDecodeError:
                                        continue
                                        
                    except Exception as e:
                        return {"success": False, "error": str(e)}
                    
                    # Calculate intervals between tokens
                    intervals = []
                    if len(token_timestamps) > 1:
                        for i in range(1, len(token_timestamps)):
                            interval = token_timestamps[i] - token_timestamps[i-1]
                            intervals.append(interval)
                    
                    return {
                        "success": True,
                        "token_count": len(tokens),
                        "intervals": intervals,
                        "avg_interval": sum(intervals) / len(intervals) if intervals else 0,
                        "max_interval": max(intervals) if intervals else 0,
                        "min_interval": min(intervals) if intervals else 0
                    }
                
                # Test multiple queries for buffering patterns
                buffering_results = []
                
                test_queries = [
                    "Short query",
                    "Medium length query about the content",
                    "Longer query requesting detailed explanation and analysis of the provided content"
                ]
                
                for i, query in enumerate(test_queries):
                    logger.info(f"Testing buffering with query {i+1}")
                    
                    result = await measure_token_intervals(query)
                    buffering_results.append(result)
                    
                    if result["success"]:
                        logger.info(f"Query {i+1}: {result['token_count']} tokens, "
                                  f"avg interval: {result['avg_interval']:.3f}s, "
                                  f"max interval: {result['max_interval']:.3f}s")
                    else:
                        logger.error(f"Query {i+1} failed: {result['error']}")
                    
                    await asyncio.sleep(1)
                
                # Analyze buffering behavior
                successful_tests = [r for r in buffering_results if r["success"]]
                
                if len(successful_tests) == 0:
                    raise Exception("No buffering tests succeeded")
                
                # Check for artificial buffering (suspiciously regular intervals)
                all_intervals = []
                for test in successful_tests:
                    all_intervals.extend(test["intervals"])
                
                if len(all_intervals) == 0:
                    raise Exception("No token intervals measured")
                
                avg_interval = sum(all_intervals) / len(all_intervals)
                max_interval = max(all_intervals)
                
                # Check for signs of artificial buffering
                # Real streaming should have variable intervals, not fixed delays
                interval_variance = sum((interval - avg_interval) ** 2 for interval in all_intervals) / len(all_intervals)
                interval_std_dev = interval_variance ** 0.5
                
                logger.info(f"Buffering analysis:")
                logger.info(f"  Total intervals measured: {len(all_intervals)}")
                logger.info(f"  Average interval: {avg_interval:.3f}s")
                logger.info(f"  Max interval: {max_interval:.3f}s")
                logger.info(f"  Standard deviation: {interval_std_dev:.3f}s")
                
                # Success criteria: No excessive delays, reasonable variance
                if (max_interval < 5.0 and  # No single interval > 5 seconds
                    avg_interval < 2.0):  # Average interval < 2 seconds
                    
                    result = TestResult(
                        test_name,
                        True,
                        time.time() - start_time,
                        response_data={
                            "successful_tests": len(successful_tests),
                            "total_intervals": len(all_intervals),
                            "avg_interval": avg_interval,
                            "max_interval": max_interval,
                            "interval_std_dev": interval_std_dev
                        }
                    )
                else:
                    result = TestResult(
                        test_name,
                        False,
                        time.time() - start_time,
                        f"Buffering delays detected: avg_interval={avg_interval:.3f}s, max_interval={max_interval:.3f}s"
                    )
                    
        except Exception as e:
            result = TestResult(test_name, False, time.time() - start_time, str(e))
            
        self.test_logger.log_test_result(result)
        self.test_logger.log_test_end(test_name)
        return result
        
    async def run_all_tests(self) -> List[TestResult]:
        """Run all streaming tests"""
        logger.info("Starting Streaming Response Tests")
        
        tests = [
            self.test_real_time_token_streaming,
            self.test_concurrent_streaming_performance,
            self.test_no_buffering_delays
        ]
        
        for test_func in tests:
            result = await test_func()
            self.results.append(result)
            
            # Pause between tests
            await asyncio.sleep(3)
            
        return self.results

# Main execution for standalone testing
async def main():
    """Run streaming tests standalone"""
    test_suite = StreamingTests()
    results = await test_suite.run_all_tests()
    
    # Summary
    passed = sum(1 for r in results if r.success)
    total = len(results)
    
    print(f"\n=== STREAMING TESTS SUMMARY ===")
    print(f"Passed: {passed}/{total}")
    print(f"Failed: {total - passed}/{total}")
    
    for result in results:
        status = "PASS" if result.success else "FAIL"
        print(f"  {status}: {result.test_name} ({result.duration:.2f}s)")
        if result.response_data:
            data = result.response_data
            if "avg_tokens_per_second" in data:
                print(f"    Tokens/sec: {data['avg_tokens_per_second']:.2f}")
            if "avg_time_to_first_token" in data:
                print(f"    Time to first token: {data['avg_time_to_first_token']:.3f}s")
        if not result.success:
            print(f"    Error: {result.error_message}")

if __name__ == "__main__":
    asyncio.run(main())
